<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDAMC Hospital - Christian Healthcare</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif'],
                        heading: ['Poppins', 'system-ui', 'sans-serif'],
                    }
                }
            }
        }
    </script>
</head>
<body class="font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm relative z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16 lg:h-20">
                <!-- Logo -->
                <div class="flex items-center space-x-2">
                    <div class="w-10 h-10 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full flex items-center justify-center">
                        <span class="text-white font-bold text-lg">S</span>
                    </div>
                    <span class="text-xl font-bold text-gray-900">SDAMC</span>
                </div>

                <!-- Desktop Navigation -->
                <nav class="hidden lg:flex items-center space-x-8">
                    <a href="#home" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Home</a>
                    <a href="#services" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Services</a>
                    <a href="#doctors" class="text-gray-700 hover:text-blue-600 font-medium transition-colors">Doctors</a>
                    <div class="flex items-center space-x-2">
                        <div class="w-6 h-6 bg-cyan-500 rounded flex items-center justify-center">
                            <div class="w-3 h-3 bg-white rounded-sm"></div>
                        </div>
                    </div>
                </nav>

                <!-- Language & CTA -->
                <div class="hidden lg:flex items-center space-x-4">
                    <span class="text-gray-600 font-medium">EN</span>
                    <button class="bg-cyan-500 hover:bg-cyan-600 text-white px-6 py-2 rounded-full font-medium transition-colors flex items-center space-x-2">
                        <span class="w-4 h-4 border border-white rounded-sm flex items-center justify-center">
                            <span class="w-2 h-2 bg-white rounded-full"></span>
                        </span>
                        <span>Book An Appointment</span>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-blue-50 via-cyan-50 to-white min-h-screen flex items-center overflow-hidden">
        <!-- Background decorative elements -->
        <div class="absolute top-20 left-10 w-20 h-20 bg-cyan-200 rounded-full opacity-30"></div>
        <div class="absolute bottom-40 left-20 w-12 h-12 bg-blue-300 rounded-full opacity-40"></div>
        <div class="absolute top-1/3 right-1/4 w-8 h-8 bg-cyan-300 rounded-full opacity-50"></div>
        
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Left Content -->
                <div class="space-y-8">
                    <!-- Stats -->
                    <div class="flex items-center space-x-4">
                        <div class="flex -space-x-2">
                            <div class="w-10 h-10 bg-gray-300 rounded-full border-2 border-white"></div>
                            <div class="w-10 h-10 bg-gray-400 rounded-full border-2 border-white"></div>
                            <div class="w-10 h-10 bg-gray-500 rounded-full border-2 border-white"></div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-gray-900">120<span class="text-lg font-normal">K+</span></div>
                            <div class="text-sm text-gray-600">Happy Patients</div>
                        </div>
                    </div>

                    <!-- Main Heading -->
                    <div class="space-y-4">
                        <h1 class="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight">
                            Trusted Healthcare with
                        </h1>
                        <h1 class="text-4xl lg:text-5xl xl:text-6xl font-bold leading-tight">
                            Compassion, <span class="text-cyan-500">Faith</span> &
                        </h1>
                        <h1 class="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight">
                            Excellence
                        </h1>
                    </div>

                    <!-- Subtitle -->
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <span class="text-gray-700 font-medium">DELIVERING</span>
                            <div class="h-px bg-gray-300 flex-1 max-w-[100px]"></div>
                        </div>
                        <p class="text-gray-700 text-lg max-w-md">
                            PATIENT-CENTERED CARE WITH MODERN MEDICAL EXPERTISE AND CHRIST-CENTERED COMPASSION.
                        </p>
                    </div>

                    <!-- Action Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button class="bg-gray-800 hover:bg-gray-900 text-white px-8 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <span>Book An Appointment</span>
                        </button>
                        <button class="bg-cyan-500 hover:bg-cyan-600 text-white px-8 py-3 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                            </svg>
                            <span>Find Doctor</span>
                        </button>
                    </div>
                </div>

                <!-- Right Content - Image and Cards -->
                <div class="relative">
                    <!-- Main Image Container -->
                    <div class="relative bg-gradient-to-br from-gray-200 to-gray-300 rounded-3xl overflow-hidden h-[500px] lg:h-[600px]">
                        <!-- Placeholder for the doctor-patient image -->
                        <div class="absolute inset-0 bg-gradient-to-br from-gray-300 to-gray-400 flex items-center justify-center">
                            <div class="text-center text-gray-600">
                                <div class="w-32 h-32 bg-white rounded-full mx-auto mb-4 flex items-center justify-center">
                                    <span class="text-4xl">👩‍⚕️</span>
                                </div>
                                <p class="text-lg font-medium">Doctor & Patient Care</p>
                                <p class="text-sm mt-2">Replace with actual image from your design</p>
                            </div>
                        </div>

                        <!-- Floating Quote Card -->
                        <div class="absolute top-6 right-6 bg-white rounded-xl p-4 shadow-lg max-w-[200px]">
                            <div class="flex items-start space-x-2">
                                <div class="w-8 h-8 bg-cyan-100 rounded flex items-center justify-center flex-shrink-0">
                                    <span class="text-cyan-600 text-sm">📖</span>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 mb-1">
                                        I am the Lord who heals you.
                                    </p>
                                    <p class="text-xs text-gray-600">Exodus 15:26</p>
                                </div>
                            </div>
                        </div>

                        <!-- Floating Arrow -->
                        <div class="absolute top-1/2 right-4 transform -translate-y-1/2 bg-white rounded-full p-3 shadow-lg">
                            <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </div>

                        <!-- Medical Specialties Tags -->
                        <div class="absolute bottom-6 left-6 right-6 space-y-2">
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
                                    Cardiologist
                                </span>
                                <span class="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
                                    Neurologist
                                </span>
                                <span class="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
                                    Dermatologist
                                </span>
                            </div>
                            <div class="flex flex-wrap gap-2">
                                <span class="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
                                    Pediatrics
                                </span>
                                <span class="bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-gray-700">
                                    Dental Care
                                </span>
                            </div>
                        </div>
                    </div>

                    <!-- Decorative Elements -->
                    <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-cyan-200 rounded-full opacity-60"></div>
                    <div class="absolute -top-4 -right-4 w-12 h-12 bg-blue-300 rounded-full opacity-50"></div>
                </div>
            </div>
        </div>
    </section>
</body>
</html>
