import React, { useState, useEffect } from 'react';
import './TestimonialsSection.css';

const TestimonialsSection: React.FC = () => {
  const testimonials = [
    {
      id: 1,
      name: '<PERSON>',
      rating: 4.5,
      testimonial: 'I recently visited Medicax for a minor injury, and I was impressed by the efficiency and professionalism of the staff. From check-in to discharge, everything went smoothly, and I felt well taken care of throughout the entire process. I highly recommend PrimeCare to anyone seeking quality medical care.'
    },
    {
      id: 2,
      name: '<PERSON>',
      rating: 4.5,
      testimonial: 'I\'ve had the pleasure of being a patient at MediCare for several years now, and I couldn\'t be happier with the level of care I\'ve received. The doctors and staff are always friendly and attentive, and I appreciate the personalized approach to my healthcare.'
    },
    {
      id: 3,
      name: '<PERSON>',
      rating: 4.5,
      testimonial: 'I\'ve been a patient at Medicax Medical Clinic for years, and I couldn\'t be happier with the level of care I receive. The staff is friendly, knowledgeable, and always goes above and beyond to ensure my needs are met. I highly recommend PrimeCare to anyone looking for a reliable medical clinic.'
    },
    {
      id: 4,
      name: '<PERSON>',
      rating: 5.0,
      testimonial: 'The medical team at Medicax is exceptional. They took the time to listen to my concerns and provided comprehensive care. The facility is modern and clean, and the staff made me feel comfortable throughout my visit. Highly recommended!'
    },
    {
      id: 5,
      name: '<PERSON> <PERSON>',
      rating: 4.8,
      testimonial: 'Outstanding service and care! The doctors are knowledgeable and compassionate. I\'ve been coming here for my family\'s healthcare needs for over 3 years, and we\'ve always received top-notch treatment. The appointment scheduling is also very convenient.'
    },
    {
      id: 6,
      name: 'Kristin Watson',
      rating: 4.7,
      testimonial: 'Medicax has been our family\'s trusted healthcare provider for years. The pediatric care for my children is excellent, and the staff is always patient and understanding. The doctors explain everything clearly and make sure we\'re comfortable with the treatment plan.'
    },
    {
      id: 7,
      name: 'Darrell Steward',
      rating: 5.0,
      testimonial: 'I was nervous about my surgery, but the surgical team at Medicax made the entire process smooth and stress-free. The post-operative care was excellent, and I recovered faster than expected. Thank you for the amazing care!'
    },
    {
      id: 8,
      name: 'Arlene McCoy',
      rating: 5.0,
      testimonial: 'The emergency department at Medicax saved my life. The quick response and professional care I received during my heart attack was incredible. The entire medical team worked seamlessly together. I\'m forever grateful for their expertise and dedication.'
    }
  ];

  const [currentIndex, setCurrentIndex] = useState(0);
  const totalSlides = testimonials.length - 2; // Show 3 cards, so we can slide through (total - 2) positions

  // Auto-scroll functionality - slide one card at a time
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % totalSlides);
    }, 4000); // 4 seconds

    return () => clearInterval(interval);
  }, [totalSlides]);

  const nextSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex + 1) % totalSlides);
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) => (prevIndex - 1 + totalSlides) % totalSlides);
  };




  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <span
        key={index}
        className={`testimonial-star ${index < rating ? 'testimonial-star-filled' : 'testimonial-star-empty'}`}
      >
        ★
      </span>
    ));
  };

  return (
    <section className="testimonials-section">
      <div className="testimonials-background"></div>
      <div className="testimonials-container">
        {/* Section Header */}
        <div className="testimonials-header">
          <div className="testimonials-badge">
            <span className="testimonials-badge-text">Medicax Testimonials</span>
          </div>
          <h2 className="testimonials-title">
            Our clients always love us
          </h2>
        </div>

        {/* Testimonials Slider */}
        <div className="testimonials-slider">
          <div
            className="testimonials-track"
            style={{ transform: `translateX(-${currentIndex * (100/3)}%)` }}
          >
            {testimonials.map((testimonial) => (
              <div key={testimonial.id} className="testimonial-slide">
                {/* Quote Icon */}
                <div className="testimonial-quote-icon">
                  <svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="20" cy="20" r="20" fill="#4A90E2" fillOpacity="0.1"/>
                    <path d="M23.35 26v-4.87c0-3.76 2.46-6.31 5.92-6.99l.66 1.42c-1.6.6-2.63 2.4-2.63 3.86h2.64V26h-6.59zm-9.24 0v-4.87c0-3.76 2.47-6.31 5.93-6.99l.66 1.42c-1.6.6-2.64 2.4-2.64 3.86h2.64V26h-6.59z" fill="#4A90E2"/>
                  </svg>
                </div>

                {/* Testimonial Text */}
                <p className="testimonial-text">
                  {testimonial.testimonial}
                </p>

                {/* Patient Info and Rating */}
                <div className="testimonial-footer">
                  <div className="testimonial-patient-info">
                    <h4 className="testimonial-patient-name">{testimonial.name}</h4>
                  </div>
                  <div className="testimonial-rating">
                    {renderStars(testimonial.rating)}
                    <span className="testimonial-rating-number">{testimonial.rating}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Navigation Controls */}
          <div className="testimonials-navigation">
            <button className="testimonials-nav-btn prev" onClick={prevSlide}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 18L9 12L15 6" stroke="#4F46E5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
            <button className="testimonials-nav-btn next" onClick={nextSlide}>
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 18L15 12L9 6" stroke="#4F46E5" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            </button>
          </div>

          {/* Dots Indicator */}
          <div className="testimonials-dots">
            {Array.from({ length: totalSlides }, (_, index) => (
              <button
                key={index}
                className={`dot ${index === currentIndex ? 'active' : ''}`}
                onClick={() => setCurrentIndex(index)}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TestimonialsSection;
