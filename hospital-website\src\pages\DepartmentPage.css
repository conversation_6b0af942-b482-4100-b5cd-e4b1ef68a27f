/* Department Page Styles */
.department-page {
  min-height: 100vh;
  background: #F4F7FF;
}

/* Page Header - Exact same as <PERSON> and <PERSON> Pages */
.page-header {
  position: relative;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.page-header-background {
  position: absolute;
  inset: 0;
  background:
    url('../assets/page-header.webp') center center no-repeat,
    url('../assets/header-bg.webp') center top / cover;
  z-index: 1;
}

.page-header-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.page-header-container {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1a1a1a;
}

.page-header-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 1rem;
  color: #6b7280;
}

.breadcrumb-item {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-item.active {
  color: #1a1a1a;
  font-weight: 600;
}

.breadcrumb-separator {
  color: #9ca3af;
}

/* Stats Section */
.department-stats {
  padding: 80px 0;
  background: white;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 32px;
  max-width: 1000px;
  margin: 0 auto;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 32px;
  background: #f8fafc;
  border-radius: 20px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #06b6d4;
}

.stat-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  line-height: 1;
  margin-bottom: 4px;
  font-family: 'Poppins', sans-serif;
}

.stat-label {
  font-size: 1rem;
  color: #6b7280;
  font-weight: 500;
}

/* Main Departments Section */
.departments-main {
  padding: 80px 0;
}

.departments-filter {
  text-align: center;
  margin-bottom: 64px;
}

.departments-section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 16px;
  font-family: 'Poppins', sans-serif;
}

.departments-section-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  max-width: 600px;
  margin: 0 auto 32px auto;
  line-height: 1.6;
}

.departments-categories {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.departments-category-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 50px;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.departments-category-btn:hover {
  border-color: #06b6d4;
  color: #06b6d4;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.15);
}

.departments-category-btn.active {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  border-color: #06b6d4;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

/* Departments Grid */
.departments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 32px;
  margin-top: 48px;
}

.department-card {
  background: white;
  border-radius: 20px;
  padding: 24px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.6s ease forwards;
  border: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 500px;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.department-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
  border-color: #06b6d4;
}

.department-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.department-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

.department-availability {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f0f9ff;
  color: #06b6d4;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.department-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.department-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
  font-family: 'Poppins', sans-serif;
  line-height: 1.3;
}

.department-description {
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 20px;
  font-size: 0.9rem;
  flex-shrink: 0;
}

.department-services {
  margin-bottom: 20px;
  flex: 1;
}

.department-services h4 {
  font-size: 0.85rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.department-service {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
  font-size: 0.85rem;
  color: #374151;
}

.department-service svg {
  color: #10b981;
  flex-shrink: 0;
}

.department-stats-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: auto;
}

.department-stat {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 0.85rem;
  color: #6b7280;
  font-weight: 500;
}

.department-stat svg {
  color: #06b6d4;
}

.department-rating {
  display: flex;
  align-items: center;
}

.department-stars {
  display: flex;
  align-items: center;
  gap: 4px;
}

.department-stars svg {
  color: #d1d5db;
}

.department-stars svg.filled {
  color: #fbbf24;
}

.department-rating-value {
  margin-left: 8px;
  font-weight: 600;
  color: #1a1a1a;
  font-size: 0.9rem;
}

.department-actions {
  display: flex;
  gap: 10px;
  margin-top: auto;
  flex-shrink: 0;
}

.department-btn-secondary,
.department-btn-primary {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 16px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.department-btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.department-btn-secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.department-btn-primary {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.department-btn-primary:hover {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

/* CTA Section */
.departments-cta {
  background: linear-gradient(135deg, #1e293b, #334155);
  padding: 80px 0;
  color: white;
}

.departments-cta-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 48px;
}

.departments-cta-text h2 {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 16px;
  font-family: 'Poppins', sans-serif;
}

.departments-cta-text p {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
}

.departments-cta-actions {
  display: flex;
  gap: 16px;
  flex-shrink: 0;
}

.departments-cta-emergency,
.departments-cta-appointment {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
  white-space: nowrap;
}

.departments-cta-emergency {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.departments-cta-emergency:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(239, 68, 68, 0.4);
}

.departments-cta-appointment {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.departments-cta-appointment:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    height: 250px;
    width: 100%;
    overflow-x: hidden;
  }

  .page-header-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .page-header-title {
    font-size: 2.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .stat-card {
    padding: 20px;
  }

  .departments-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .department-card {
    padding: 20px;
    min-height: auto;
  }

  .department-title {
    font-size: 1.25rem;
  }

  .department-description {
    font-size: 0.85rem;
  }

  .departments-cta-content {
    flex-direction: column;
    text-align: center;
    gap: 32px;
  }

  .departments-cta-actions {
    flex-direction: column;
    width: 100%;
  }

  .departments-cta-emergency,
  .departments-cta-appointment {
    width: 100%;
    justify-content: center;
  }

  .department-stats-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .departments-categories {
    flex-direction: column;
    align-items: center;
  }

  .departments-category-btn {
    width: 200px;
    justify-content: center;
  }
}
