import React, { useState, useEffect } from 'react';
import Header from '../components/Header';
import Footer from '../components/Footer';
import BookingModal from '../components/BookingModal';
import {
  Heart,
  Brain,
  Eye,
  Bone,
  Baby,
  Stethoscope,
  Activity,
  Shield,
  Clock,
  Award,
  Users,
  Star,
  ArrowRight,
  CheckCircle,
  Phone,
  Calendar,
  MapPin
} from 'lucide-react';
import pageHeaderBg from '../assets/page-header.webp';
import './ServicesPage.css';

interface Service {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  features: string[];
  image: string;
  category: string;
  availability: string;
  rating: number;
  reviews: number;
}

const ServicesPage: React.FC = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isVisible, setIsVisible] = useState(false);
  const [isBookingOpen, setIsBookingOpen] = useState(false);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const handleBookAppointment = () => {
    setIsBookingOpen(true);
  };

  const services: Service[] = [
    {
      id: 'lifestyle-medicine',
      title: 'Lifestyle Medicine',
      description: 'Lifestyle medicine focuses on addressing the underlying causes of chronic diseases through lifestyle interventions such as nutrition, physical activity, stress management, and sleep hygiene.',
      icon: <Heart size={32} />,
      features: ['Nutrition Counseling', 'Physical Activity Plans', 'Stress Management', 'Sleep Hygiene'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: 'Mon-Sat',
      rating: 4.8,
      reviews: 156
    },
    {
      id: 'physiotherapy',
      title: 'Physiotherapy',
      description: 'Physiotherapy is an important part of healthcare services. Physiotherapists use a variety of techniques to help patients recover from injuries, illnesses, surgeries.',
      icon: <Activity size={32} />,
      features: ['Injury Recovery', 'Post-Surgery Rehab', 'Pain Management', 'Mobility Training'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: 'Mon-Sat',
      rating: 4.7,
      reviews: 189
    },
    {
      id: 'ophthalmology',
      title: 'Ophthalmology',
      description: 'The Ophthalmology Department caters to all eye and vision related problems. We do regular eye check-ups for adults, children and infants.',
      icon: <Eye size={32} />,
      features: ['Vision Exams', 'Slit Lamp Examinations', 'Eye Surgery', 'Pediatric Eye Care'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: 'Mon-Sat',
      rating: 4.9,
      reviews: 234
    },
    {
      id: 'ent',
      title: 'Ear, Nose and Throat',
      description: 'The ENT department specializes in treating conditions and diseases related to the ears, nose, and throat.',
      icon: <Stethoscope size={32} />,
      features: ['Hearing Tests', 'Throat Surgery', 'Sinus Treatment', 'Voice Disorders'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: 'Mon-Sat',
      rating: 4.6,
      reviews: 167
    },
    {
      id: 'general-medicine',
      title: 'General Medicine',
      description: 'The department of General Medicine provides comprehensive care for adults with various medical ailments. We offer a range of services.',
      icon: <Stethoscope size={32} />,
      features: ['Health Checkups', 'Chronic Disease Management', 'Preventive Care', 'Adult Medicine'],
      image: '/api/placeholder/400/300',
      category: 'primary',
      availability: '24/7',
      rating: 4.8,
      reviews: 312
    },
    {
      id: 'orthopedics',
      title: 'Orthopedics',
      description: 'Orthopedic services focus on the diagnosis, treatment, and rehabilitation of conditions and injuries of the musculoskeletal system.',
      icon: <Bone size={32} />,
      features: ['Joint Replacement', 'Fracture Care', 'Sports Medicine', 'Spine Surgery'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: '24/7',
      rating: 4.7,
      reviews: 198
    },
    {
      id: 'paediatrics',
      title: 'Paediatrics',
      description: 'The Paediatrics and Neonatology department provides expert care for newborns and children up to 15 years of age.',
      icon: <Baby size={32} />,
      features: ['Newborn Care', 'Child Health', 'Vaccinations', 'Growth Monitoring'],
      image: '/api/placeholder/400/300',
      category: 'primary',
      availability: 'Mon-Sat',
      rating: 4.9,
      reviews: 278
    },
    {
      id: 'cardiology',
      title: 'Cardiology',
      description: 'The cardiology department focuses on the diagnosis and treatment of diseases and conditions of the heart and cardiovascular system.',
      icon: <Heart size={32} />,
      features: ['Heart Diagnosis', 'Cardiovascular Treatment', 'ECG Services', 'Heart Surgery'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: '24/7',
      rating: 4.9,
      reviews: 245
    },
    {
      id: 'obstetrics-gynaecology',
      title: 'Obstetrics and Gynaecology',
      description: 'The department of Obstetrics and Gynaecology is the hospital\'s flagship speciality. We have provided expert care to women for over 30 years.',
      icon: <Heart size={32} />,
      features: ['Pregnancy Care', 'Gynecological Surgery', 'Family Planning', 'Women\'s Health'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: '24/7',
      rating: 4.8,
      reviews: 356
    },
    {
      id: 'laboratory',
      title: '24-Hour Laboratory',
      description: 'Laboratory services refer to medical testing and analysis performed on patient specimens, such as blood, urine, and tissues, to diagnose and monitor diseases.',
      icon: <Activity size={32} />,
      features: ['Blood Tests', 'Urine Analysis', 'Tissue Testing', '24/7 Service'],
      image: '/api/placeholder/400/300',
      category: 'diagnostic',
      availability: '24/7',
      rating: 4.7,
      reviews: 189
    },
    {
      id: 'radiology',
      title: 'Radiology',
      description: 'Radiology service uses imaging techniques, such as X-rays, CT scans, MRI, and ultrasound, to diagnose and treat diseases and conditions.',
      icon: <Activity size={32} />,
      features: ['X-rays', 'CT Scans', 'MRI', 'Ultrasound'],
      image: '/api/placeholder/400/300',
      category: 'diagnostic',
      availability: '24/7',
      rating: 4.8,
      reviews: 234
    },
    {
      id: 'general-laparoscopic-surgery',
      title: 'General & Laparoscopic Surgery',
      description: 'The General and Laparoscopic Surgery department is committed to providing compassionate and quality care for patients needing surgical intervention.',
      icon: <Activity size={32} />,
      features: ['Minimally Invasive Surgery', 'Emergency Surgery', 'Elective Procedures', 'Post-op Care'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: '24/7',
      rating: 4.9,
      reviews: 167
    },
    {
      id: 'gastroenterology',
      title: 'Gastroenterology',
      description: 'The gastroenterology department specializes in diagnosis and treatment of problems relating to the esophagus, stomach and intestines.',
      icon: <Activity size={32} />,
      features: ['Endoscopy', 'Digestive Disorders', 'Liver Care', 'Intestinal Treatment'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: 'Mon-Sat',
      rating: 4.7,
      reviews: 145
    },
    {
      id: 'dermatology',
      title: 'Dermatology',
      description: 'Dermatology deals with the diagnosis and treatment of skin, hair, and nail disorders. Our dermatologists work closely with other medical professionals.',
      icon: <Shield size={32} />,
      features: ['Skin Treatment', 'Hair Care', 'Nail Disorders', 'Cosmetic Procedures'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: 'Mon-Sat',
      rating: 4.6,
      reviews: 123
    },
    {
      id: 'anaesthesiology',
      title: 'Anaesthesiology',
      description: 'Anesthesiology involves the administration of anesthesia to patients undergoing surgical procedures or other medical interventions.',
      icon: <Activity size={32} />,
      features: ['Surgical Anesthesia', 'Pain Management', 'Critical Care', 'Patient Monitoring'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: '24/7',
      rating: 4.8,
      reviews: 98
    },
    {
      id: 'pulmonology',
      title: 'Pulmonology',
      description: 'Pulmonology focuses on the diagnosis and treatment of lung and respiratory system diseases. We manage a wide range of respiratory conditions.',
      icon: <Activity size={32} />,
      features: ['Lung Function Tests', 'Respiratory Care', 'Asthma Treatment', 'Sleep Studies'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: 'Mon-Sat',
      rating: 4.7,
      reviews: 156
    },
    {
      id: 'nephrology',
      title: 'Nephrology',
      description: 'Nephrology focuses on the diagnosis, treatment, and management of diseases that affect the kidneys. Our nephrologists work with healthcare professionals.',
      icon: <Activity size={32} />,
      features: ['Kidney Care', 'Dialysis', 'Transplant Support', 'Chronic Kidney Disease'],
      image: '/api/placeholder/400/300',
      category: 'specialty',
      availability: 'Mon-Sat',
      rating: 4.8,
      reviews: 134
    }
  ];

  const categories = [
    { id: 'all', name: 'All Services', icon: <Stethoscope size={20} /> },
    { id: 'specialty', name: 'Specialty Care', icon: <Heart size={20} /> },
    { id: 'primary', name: 'Primary Care', icon: <Shield size={20} /> },
    { id: 'diagnostic', name: 'Diagnostic Services', icon: <Activity size={20} /> }
  ];

  const filteredServices = selectedCategory === 'all' 
    ? services 
    : services.filter(service => service.category === selectedCategory);

  const stats = [
    { icon: <Users size={24} />, value: '50,000+', label: 'Patients Served' },
    { icon: <Award size={24} />, value: '25+', label: 'Specialties' },
    { icon: <Clock size={24} />, value: '24/7', label: 'Emergency Care' },
    { icon: <Star size={24} />, value: '4.8', label: 'Average Rating' }
  ];

  return (
    <div className="services-page">
      <Header />

      {/* Page Header */}
      <section className="page-header">
        <div
          className="page-header-background"
          // style={{ backgroundImage: `url(${pageHeaderBg})` }}
        >
          <div className="page-header-overlay"></div>
        </div>
        <div className="page-header-container">
          <div className="page-header-content">
            <h1 className="page-header-title">Our Services</h1>
            <div className="page-header-breadcrumb">
              <span className="breadcrumb-item">Home</span>
              <span className="breadcrumb-separator">&gt;</span>
              <span className="breadcrumb-item active">Services</span>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="services-main">
        <div className="container-custom">
          {/* Category Filter */}
          <div className="services-filter">
            <h2 className="services-section-title">Our Medical Services</h2>
            <div className="services-categories">
              {categories.map((category) => (
                <button
                  key={category.id}
                  className={`services-category-btn ${selectedCategory === category.id ? 'active' : ''}`}
                  onClick={() => setSelectedCategory(category.id)}
                >
                  {category.icon}
                  <span>{category.name}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Services Grid */}
          <div className="services-grid">
            {filteredServices.map((service, index) => (
              <div 
                key={service.id} 
                className="service-card"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="service-card-header">
                  <div className="service-icon">{service.icon}</div>
                  <div className="service-availability">
                    <Clock size={14} />
                    <span>{service.availability}</span>
                  </div>
                </div>
                
                <div className="service-content">
                  <h3 className="service-title">{service.title}</h3>
                  <p className="service-description">{service.description}</p>
                  
                  <div className="service-features">
                    {service.features.map((feature, idx) => (
                      <div key={idx} className="service-feature">
                        <CheckCircle size={16} />
                        <span>{feature}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="service-rating">
                    <div className="service-stars">
                      {[...Array(5)].map((_, i) => (
                        <Star 
                          key={i} 
                          size={16} 
                          className={i < Math.floor(service.rating) ? 'filled' : ''} 
                        />
                      ))}
                      <span className="service-rating-value">{service.rating}</span>
                    </div>
                    <span className="service-reviews">({service.reviews} reviews)</span>
                  </div>
                </div>
                
                <div className="service-actions">
                  <button className="service-btn-secondary">
                    <Phone size={16} />
                    Call Now
                  </button>
                  <button
                    className="service-btn-primary"
                    onClick={handleBookAppointment}
                  >
                    <Calendar size={16} />
                    Book Appointment
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="services-cta">
        <div className="container-custom">
          <div className="services-cta-content">
            <div className="services-cta-text">
              <h2>Need Immediate Medical Attention?</h2>
              <p>Our emergency department is open 24/7 with expert medical staff ready to help.</p>
            </div>
            <div className="services-cta-actions">
              <button className="services-cta-emergency">
                <Activity size={20} />
                Emergency: 911
              </button>
              <button className="services-cta-location">
                <MapPin size={20} />
                Find Location
              </button>
            </div>
          </div>
        </div>
      </section>

      <Footer />

      {/* Booking Modal */}
      <BookingModal
        isOpen={isBookingOpen}
        onClose={() => setIsBookingOpen(false)}
      />
    </div>
  );
};

export default ServicesPage;
