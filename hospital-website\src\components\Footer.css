.footer {
  background-color: #111827;
  color: white;
}

.footer-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 48px 16px;
}

@media (min-width: 640px) {
  .footer-container {
    padding: 56px 24px;
  }
}

@media (min-width: 768px) {
  .footer-container {
    padding: 64px 24px;
  }
}

@media (min-width: 1024px) {
  .footer-container {
    padding: 64px 32px;
  }
}

.footer-grid {
  display: grid;
  gap: 32px;
}

@media (min-width: 768px) {
  .footer-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .footer-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

.footer-section {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.footer-logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.footer-logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(to bottom right, #22d3ee, #3b82f6);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.footer-logo-text {
  color: white;
  font-weight: bold;
  font-size: 18px;
}

.footer-logo-name {
  font-size: 24px;
  font-weight: bold;
}

.footer-description {
  color: #d1d5db;
  line-height: 1.6;
}

.footer-social {
  display: flex;
  gap: 16px;
}

.footer-social-link {
  width: 40px;
  height: 40px;
  background-color: #1f2937;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s ease;
  text-decoration: none;
  color: white;
}

.footer-social-link:hover {
  background-color: #0891b2;
}

.footer-social-link svg {
  width: 20px;
  height: 20px;
}

.footer-heading {
  font-size: 20px;
  font-weight: bold;
  margin-bottom: 24px;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-link {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-link:hover {
  color: #22d3ee;
}

.footer-contact {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer-contact-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.footer-contact-icon {
  width: 20px;
  height: 20px;
  color: #22d3ee;
  margin-top: 4px;
  flex-shrink: 0;
}

.footer-contact-text {
  color: #d1d5db;
  line-height: 1.5;
}

.footer-contact-phones {
  display: flex;
  flex-direction: column;
}

.footer-contact-hours {
  display: flex;
  flex-direction: column;
}

.footer-bottom {
  border-top: 1px solid #1f2937;
  margin-top: 48px;
  padding-top: 32px;
}

.footer-bottom-content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

@media (min-width: 768px) {
  .footer-bottom-content {
    flex-direction: row;
    gap: 0;
  }
}

.footer-copyright {
  color: #9ca3af;
  font-size: 14px;
}

.footer-legal {
  display: flex;
  gap: 24px;
  font-size: 14px;
}

.footer-legal-link {
  color: #9ca3af;
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-legal-link:hover {
  color: #22d3ee;
}

.footer-emergency {
  background-color: #dc2626;
  padding: 12px 0;
}

.footer-emergency-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 640px) {
  .footer-emergency-container {
    padding: 0 24px;
  }
}

@media (min-width: 1024px) {
  .footer-emergency-container {
    padding: 0 32px;
  }
}

.footer-emergency-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  color: white;
}

.footer-emergency-icon {
  width: 20px;
  height: 20px;
}

.footer-emergency-text {
  font-weight: 500;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .footer {
    width: 100%;
    overflow-x: hidden;
  }

  .footer-container {
    padding: 30px 16px;
    width: 100%;
    max-width: 100%;
  }

  .footer-grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }

  .footer-section {
    text-align: center;
  }

  .footer-social {
    justify-content: center;
  }

  .footer-legal {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}

/* Extra small mobile adjustments */
@media (max-width: 640px) {
  .footer-grid {
    gap: 24px;
  }
  
  .footer-section {
    gap: 16px;
  }
  
  .footer-heading {
    font-size: 18px;
    margin-bottom: 16px;
  }
  
  .footer-social {
    justify-content: center;
  }
  
  .footer-legal {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}
