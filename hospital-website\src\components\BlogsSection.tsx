import React from 'react';
import './BlogsSection.css';

const BlogsSection: React.FC = () => {
  const blogs = [
    {
      id: 1,
      title: 'Simple Steps to a Healthier Life',
      category: 'Health Tips',
      image: 'blog1',
      gradient: 'linear-gradient(135deg, #a7f3d0 0%, #67e8f9 100%)',
      textColor: 'white'
    },
    {
      id: 2,
      title: 'Wellness Matters: Simple Steps to a Healthier Life',
      category: 'Wellness',
      image: 'blog2',
      gradient: 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
      textColor: 'white'
    },
    {
      id: 3,
      title: 'Holistic Tips for Balanced Living',
      category: 'Lifestyle',
      image: 'blog3',
      gradient: 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
      textColor: 'white'
    },
    {
      id: 4,
      title: 'Faith-Filled Approaches to Wellness',
      category: 'Faith & Health',
      image: 'blog4',
      gradient: 'linear-gradient(135deg, #0891b2 0%, #0e7490 100%)',
      textColor: 'white'
    }
  ];

  return (
    <section className="blogs-section">
      <div className="blogs-container">
        <div className="blogs-header">
          <div className="blogs-title-section">
            <h2 className="blogs-main-title">
              Feel free to explore our content & blogs
            </h2>
          </div>
          <div className="blogs-description-section">
            <p className="blogs-description">
              STAY INFORMED WITH EXPERT HEALTH TIPS, MEDICAL NEWS, AND FAITH-BASED WELLNESS INSIGHTS—CURATED JUST FOR YOU.
            </p>
          </div>
        </div>

        <div className="blogs-grid">
          {blogs.map((blog) => (
            <div key={blog.id} className="blog-card" style={{ background: blog.gradient }}>
              <div className="blog-card-content">
                <div className="blog-arrow-icon">
                  <span>↗</span>
                </div>
                <div className="blog-text-content">
                  <h3 className="blog-title" style={{ color: blog.textColor }}>
                    {blog.title}
                  </h3>
                  <button className="blog-learn-more" style={{ color: blog.textColor }}>
                    Learn More
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default BlogsSection;
