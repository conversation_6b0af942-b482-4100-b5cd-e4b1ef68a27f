.about-section {
  padding: 60px 0;
  background-color: #F4F7FF;
}

@media (min-width: 768px) {
  .about-section {
    padding: 80px 0;
  }
}

.about-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 640px) {
  .about-container {
    padding: 0 24px;
  }
}

@media (min-width: 1024px) {
  .about-container {
    padding: 0 32px;
  }
}

.about-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 60px;
  padding: 24px;
  border-radius: 16px;
  background-color: #E8EFFF;
}

@media (min-width: 640px) {
  .about-stats {
    gap: 28px;
    padding: 28px;
  }
}

@media (min-width: 768px) {
  .about-stats {
    gap: 32px;
    margin-bottom: 80px;
    padding: 32px;
  }
}

@media (min-width: 1024px) {
  .about-stats {
    grid-template-columns: repeat(4, 1fr);
  }
}

.about-stat-item {
  text-align: center;
}

.about-stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 8px;
}

@media (min-width: 640px) {
  .about-stat-number {
    font-size: 32px;
  }
}

@media (min-width: 768px) {
  .about-stat-number {
    font-size: 36px;
  }
}

@media (min-width: 1024px) {
  .about-stat-number {
    font-size: 48px;
  }
}

.about-stat-label {
  font-size: 14px;
  color: #6b7280;
  font-weight: 500;
  letter-spacing: 0.05em;
}

.about-content {
  display: grid;
  gap: 48px;
  align-items: center;
}

@media (min-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr 1fr;
  }
}

.about-video-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* height: 400px; */
}

.about-video-wrapper {
  position: relative;
  border-radius: 24px;
  overflow: hidden;
  width: 70%;
  height: 400px;
}

@media (min-width: 1024px) {
  .about-video-wrapper {
    height: 500px;
  }
}

.about-video-background {
  position: absolute;
  inset: 0;
  background: url(../assets/hospitalImg.JPG);
  background-size: cover;
  background-position: center;
}

.about-video-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
}

.about-video-placeholder {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-video-placeholder-content {
  text-align: center;
  color: white;
  opacity: 0.3;
}

.about-video-placeholder-icon {
  width: 128px;
  height: 128px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36px;
}

.about-video-placeholder-text {
  font-size: 18px;
  font-weight: 500;
}

.about-play-button {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-play-btn {
  width: 80px;
  height: 80px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  border: none;
  cursor: pointer;
}

.about-play-btn:hover {
  transform: scale(1.1);
}

.about-play-icon {
  width: 32px;
  height: 32px;
  margin-left: 4px;
}

.about-emergency-card {
  
  background-color: #E8EFFF;
  /* border-radius: 16px; */
  border-top-right-radius: 16px;
  border-bottom-right-radius: 16px;
  padding: 16px;
  padding-left: 26px;
  /* box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); */
  width: 35%;
  height: 500px;
  margin-left: -10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.about-emergency-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.about-emergency-icon {
  width: 40px;
  height: 40px;
  background-color: #ecfeff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.about-emergency-icon svg {
  width: 20px;
  height: 20px;
  color: #0891b2;
}

.about-emergency-badge {
  /* background-color: #f3f4f6; */
  color: #374151;
  padding: 5px 12px;
  border-radius: 9999px;
  border: 1px solid #000;
  font-size: 14px;
  font-weight: 500;
}

.about-emergency-title {
  font-size: 20px;
  color: #111827;
  font-weight: 600;
  margin-bottom: 4px;
  line-height: 1;
  font-family: 'Poppins';
}

.about-emergency-subtitle {
  font-size: 14px;
  color: #111827;
  font-weight: 500;
  margin: 12px 0;
  line-height: 1.3;
  font-family: 'Poppins';
}

.about-emergency-text {
  font-size: 12px;
  color: #6b7280;
  line-height: 1.4;
}

.about-decorative-1 {
  position: absolute;
  bottom: -16px;
  left: -16px;
  width: 64px;
  height: 64px;
  background-color: #a7f3d0;
  border-radius: 50%;
  opacity: 0.6;
}

.about-decorative-2 {
  position: absolute;
  top: -16px;
  right: -16px;
  width: 48px;
  height: 48px;
  background-color: #93c5fd;
  border-radius: 50%;
  opacity: 0.5;
}

.about-text-content {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.about-badge {
  display: inline-block;
}

.about-badge-text {
  background-color: #06b6d4;
  color: white;
  padding: 8px 24px;
  border-radius: 9999px;
  font-size: 14px;
  font-weight: 500;
}

.about-heading-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.about-heading {
  font-size: 36px;
  font-weight: bold;
  color: #111827;
  line-height: 1.2;
}

@media (min-width: 1024px) {
  .about-heading {
    font-size: 48px;
  }
}

.about-heading-highlight {
  color: #06b6d4;
  padding: 0 20px;
  margin: 0 8px;
  background: linear-gradient(to right, #F0EBFF, #D0DFFF);
  border-radius: 52px;
  line-height: 1;
}

.about-learn-more {
  display: flex;
}

.about-learn-btn {
  background-color: #fff;
  color: #000;
  padding: 12px 12px;
  border-radius: 9999px;
  font-family: 'Poppins';
  font-weight: 500;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  border: none;
  cursor: pointer;
  font-size: 16px;
  border: 1px solid #497DFF;
  transition: all 0.3s ease-in-out;
}

.about-learn-btn:hover {
  background-color: #ccc;
  padding: 12px 24px;
}

.about-learn-icon {
  width: 24px;
  height: 24px;
  background-color: #000;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: rotate(318deg);
}

.about-learn-icon svg {
  width: 14px;
  height: 14px;
  color: #fff;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .about-section {
    padding: 40px 0;
    width: 100%;
    overflow-x: hidden;
  }

  .about-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .about-stats {
    grid-template-columns: 1fr;
    gap: 16px;
    margin-bottom: 40px;
    padding: 20px;
  }

  .about-stat-number {
    font-size: 28px;
  }

  .about-stat-label {
    font-size: 12px;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .about-video-container {
    order: 2;
    flex-direction: column;
  }

  .about-text-content {
    order: 1;
    /* text-align: center; */
  }

  .about-title {
    font-size: 28px;
    line-height: 1.2;
  }

  .about-description {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .about-features {
    gap: 16px;
  }

  .about-feature {
    padding: 16px;
  }

  .about-feature-title {
    font-size: 16px;
  }

  .about-feature-description {
    font-size: 14px;
  }

  .about-learn-btn {
    font-size: 14px;
    padding: 10px 10px;
  }

  .about-video-wrapper{
    width: 100%;
  }

  .about-play-btn{
    display: none;
  }

  .about-emergency-header{
    gap: 0px;
  }

  .about-emergency-title{
    margin-left: 15px;
    margin-bottom: 0px;
  }

  .about-emergency-card{
    height: 100%;
    width: 100%;
    margin-top: -5px;
    margin-left: 0px;
    border-radius: 0px;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    display: flex;
    flex-direction: row;
    padding: 10px;
    padding-top: 25px;
  }
}

@media (max-width: 480px) {
  .about-section {
    padding: 30px 0;
  }

  .about-container {
    padding: 0 12px;
  }

  .about-stats {
    padding: 16px;
    gap: 12px;
  }

  .about-stat-number {
    font-size: 24px;
  }

  .about-stat-label {
    font-size: 11px;
  }

  .about-title {
    font-size: 24px;
  }

  .about-description {
    font-size: 15px;
  }

  .about-feature {
    padding: 12px;
  }

  .about-feature-title {
    font-size: 15px;
  }

  .about-feature-description {
    font-size: 13px;
  }
}
