@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Global Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  color: #111827;
  background-color: #F4F7FF;
  font-feature-settings: "rlig" 1, "calt" 1;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 0.5em;
}

p {
  margin-bottom: 1em;
}

a {
  color: inherit;
  text-decoration: none;
}

/* Utility Classes */
.container-custom {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;
}

@media (min-width: 640px) {
  .container-custom {
    padding: 0 24px;
  }
}

@media (min-width: 1024px) {
  .container-custom {
    padding: 0 32px;
  }
}

/* Button Base Styles */
.btn-primary {
  background-color: #06b6d4;
  color: white;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background-color: #0891b2;
}

.btn-secondary {
  background-color: #f3f4f6;
  color: #374151;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-secondary:hover {
  background-color: #e5e7eb;
}

/* Card Component */
.card {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
}

/* Focus Styles */
button:focus,
input:focus,
select:focus,
textarea:focus {
  outline: 2px solid #06b6d4;
  outline-offset: 2px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Animation Utilities */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.6s ease-out;
}

/* Responsive Text Sizes */
.text-responsive-xl {
  font-size: 24px;
}

@media (min-width: 768px) {
  .text-responsive-xl {
    font-size: 32px;
  }
}

@media (min-width: 1024px) {
  .text-responsive-xl {
    font-size: 40px;
  }
}

.text-responsive-lg {
  font-size: 20px;
}

@media (min-width: 768px) {
  .text-responsive-lg {
    font-size: 24px;
  }
}

@media (min-width: 1024px) {
  .text-responsive-lg {
    font-size: 28px;
  }
}

/* Mobile-First Responsive Utilities */
@media (max-width: 768px) {
  /* Ensure proper mobile viewport */
  html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    width: 100%;
    overflow-x: hidden;
  }

  /* Improve touch targets */
  button,
  input[type="button"],
  input[type="submit"],
  a {
    min-height: 44px;
    min-width: 44px;
  }

  /* Better mobile typography */
  body {
    font-size: 16px;
    line-height: 1.5;
    width: 100%;
    overflow-x: hidden;
  }

  /* Ensure all elements use border-box */
  *, *::before, *::after {
    box-sizing: border-box;
  }

  /* Mobile container adjustments */
  .container-custom {
    padding: 0 16px;
    max-width: 100%;
    width: 100%;
  }

  /* Mobile button improvements */
  .btn-primary,
  .btn-secondary {
    padding: 12px 20px;
    font-size: 16px;
    border-radius: 8px;
    min-height: 44px;
    width: 100%;
    max-width: 300px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
  }

  /* Mobile card improvements */
  .card {
    border-radius: 12px;
    margin-bottom: 16px;
  }

  /* Mobile text utilities */
  .text-responsive-xl {
    font-size: 28px;
    line-height: 1.2;
  }

  .text-responsive-lg {
    font-size: 22px;
    line-height: 1.3;
  }

  /* Mobile image responsiveness */
  img {
    max-width: 100%;
    height: auto;
  }

  /* Mobile horizontal scroll containers */
  .mobile-scroll {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    -webkit-overflow-scrolling: touch;
    gap: 16px;
    padding: 0 16px;
  }

  .mobile-scroll::-webkit-scrollbar {
    display: none;
  }

  .mobile-scroll > * {
    flex: 0 0 auto;
    scroll-snap-align: start;
  }

  /* Mobile grid adjustments */
  .grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  /* Mobile section spacing */
  .section {
    padding: 40px 0;
  }
}

@media (max-width: 480px) {
  /* Extra small mobile adjustments */
  .container-custom {
    padding: 0 12px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 10px 16px;
    font-size: 15px;
  }

  .text-responsive-xl {
    font-size: 24px;
  }

  .text-responsive-lg {
    font-size: 20px;
  }

  /* Extra small mobile section spacing */
  .section {
    padding: 30px 0;
  }

  /* Extra small mobile horizontal scroll */
  .mobile-scroll {
    padding: 0 12px;
    gap: 12px;
  }
}

/* Mobile-specific utilities */
.mobile-hidden {
  display: block;
}

.mobile-only {
  display: none;
}

@media (max-width: 768px) {
  .mobile-hidden {
    display: none;
  }

  .mobile-only {
    display: block;
  }
}

/* Touch-friendly scrolling */
.horizontal-scroll {
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.horizontal-scroll::-webkit-scrollbar {
  display: none;
}
