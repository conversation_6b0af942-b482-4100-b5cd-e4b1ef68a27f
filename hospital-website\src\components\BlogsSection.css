.blogs-section {
  background-color: #F4F7FF;
  padding: 80px 0;
  min-height: 600px;
}

.blogs-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.blogs-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 60px;
  gap: 40px;
}

.blogs-title-section {
  flex: 1;
  max-width: 500px;
}

.blogs-main-title {
  font-size: 48px;
  font-weight: 700;
  line-height: 1.2;
  color: #1a1a1a;
  margin: 0;
  font-family: 'Inter', sans-serif;
}

.blogs-description-section {
  flex: 1;
  max-width: 500px;
  display: flex;
  align-items: center;
}

.blogs-description {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.6;
  color: #666;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-family: 'Inter', sans-serif;
}

.blogs-grid {
  display: flex;
  gap: 20px;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 10px;
}

.blogs-grid::-webkit-scrollbar {
  display: none;
}

@media (min-width: 768px) {
  .blogs-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    overflow-x: visible;
    scroll-snap-type: none;
    padding-bottom: 0;
  }
}

@media (min-width: 1024px) {
  .blogs-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 24px;
  }
}

.blog-card {
  border-radius: 20px;
  padding: 32px 24px;
  height: 280px;
  position: relative;
  overflow: hidden;
  cursor: pointer;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-width: 280px;
  flex-shrink: 0;
  scroll-snap-align: start;
}

@media (min-width: 768px) {
  .blog-card {
    min-width: auto;
    flex-shrink: 1;
    scroll-snap-align: none;
  }
}

.blog-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.blog-card-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.blog-arrow-icon {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.blog-arrow-icon span {
  width: 32px;
  height: 32px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 600;
  color: white;
  transition: background-color 0.3s ease;
}

.blog-card:hover .blog-arrow-icon span {
  background-color: rgba(255, 255, 255, 0.3);
}

.blog-text-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.blog-title {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
  margin: 0 0 20px 0;
  font-family: 'Inter', sans-serif;
}

.blog-learn-more {
  background: none;
  border: none;
  font-size: 16px;
  font-weight: 500;
  text-decoration: underline;
  cursor: pointer;
  padding: 0;
  text-align: left;
  font-family: 'Inter', sans-serif;
  transition: opacity 0.3s ease;
}

.blog-learn-more:hover {
  opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .blogs-header {
    flex-direction: column;
    gap: 30px;
    text-align: center;
  }

  .blogs-title-section,
  .blogs-description-section {
    max-width: 100%;
  }

  .blogs-main-title {
    font-size: 40px;
  }
}

@media (max-width: 768px) {
  .blogs-section {
    padding: 60px 0;
    width: 100%;
    overflow-x: hidden;
  }

  .blogs-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .blogs-main-title {
    font-size: 32px;
  }

  .blogs-description {
    font-size: 13px;
  }

  .blogs-grid {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    gap: 16px;
    padding: 0 16px 8px 16px;
    margin: 0 -16px;
    -webkit-overflow-scrolling: touch;
    width: 100%;
  }

  .blogs-grid::-webkit-scrollbar {
    display: none;
  }

  .blog-card {
    height: 240px;
    padding: 24px 20px;
    min-width: 260px;
    flex: 0 0 auto;
    scroll-snap-align: start;
  }

  .blog-title {
    font-size: 20px;
  }
}
