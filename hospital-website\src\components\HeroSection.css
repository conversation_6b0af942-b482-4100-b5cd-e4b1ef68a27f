.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  /* background: linear-gradient(135deg, #F4F7FF 0%, #E8F4FD 50%, #F0F9FF 100%); */
}

/* Animated Background Particles */
.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="particle1" cx="50%" cy="50%"><stop offset="0%" stop-color="%2306b6d4" stop-opacity="0.1"/><stop offset="100%" stop-color="%2306b6d4" stop-opacity="0"/></radialGradient><radialGradient id="particle2" cx="50%" cy="50%"><stop offset="0%" stop-color="%23a7f3d0" stop-opacity="0.08"/><stop offset="100%" stop-color="%23a7f3d0" stop-opacity="0"/></radialGradient></defs><circle cx="100" cy="100" r="50" fill="url(%23particle1)"/><circle cx="300" cy="200" r="30" fill="url(%23particle2)"/><circle cx="500" cy="150" r="40" fill="url(%23particle1)"/><circle cx="700" cy="300" r="35" fill="url(%23particle2)"/><circle cx="200" cy="400" r="25" fill="url(%23particle1)"/><circle cx="600" cy="450" r="45" fill="url(%23particle2)"/><circle cx="800" cy="100" r="30" fill="url(%23particle1)"/><circle cx="400" cy="500" r="35" fill="url(%23particle2)"/></svg>') repeat;
  animation: floatParticles 20s ease-in-out infinite;
  opacity: 0.6;
  z-index: 1;
}

@keyframes floatParticles {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  25% { transform: translateY(-20px) rotate(90deg); }
  50% { transform: translateY(-10px) rotate(180deg); }
  75% { transform: translateY(-30px) rotate(270deg); }
}

.hero-bg-element-1 {
  position: absolute;
  top: 80px;
  left: 40px;
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #a7f3d0, #34d399);
  border-radius: 50%;
  opacity: 0.4;
  animation: float1 6s ease-in-out infinite;
  box-shadow: 0 10px 30px rgba(167, 243, 208, 0.3);
}

.hero-bg-element-2 {
  position: absolute;
  bottom: 160px;
  left: 80px;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #93c5fd, #3b82f6);
  border-radius: 50%;
  opacity: 0.5;
  animation: float2 8s ease-in-out infinite;
  box-shadow: 0 8px 25px rgba(147, 197, 253, 0.4);
}

.hero-bg-element-3 {
  position: absolute;
  top: 33.333333%;
  right: 25%;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #67e8f9, #06b6d4);
  border-radius: 50%;
  opacity: 0.6;
  animation: float3 7s ease-in-out infinite;
  box-shadow: 0 6px 20px rgba(103, 232, 249, 0.4);
}

@keyframes float1 {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-20px) scale(1.1); }
}

@keyframes float2 {
  0%, 100% { transform: translateY(0px) scale(1) rotate(0deg); }
  50% { transform: translateY(-15px) scale(1.05) rotate(180deg); }
}

@keyframes float3 {
  0%, 100% { transform: translateY(0px) scale(1); }
  33% { transform: translateY(-10px) scale(1.15); }
  66% { transform: translateY(-25px) scale(0.95); }
}

.hero-capsule {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-80%);
  z-index: 5;
  animation: capsuleBounce 3s ease-in-out infinite;
}

.hero-capsule img {
  width: 64px;
  height: 64px;
  opacity: 0.8;
  filter: drop-shadow(0 10px 15px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease, filter 0.3s ease;
}

.hero-capsule:hover img {
  transform: scale(1.2) rotate(15deg);
  filter: drop-shadow(0 15px 20px rgba(0, 0, 0, 0.2));
}

@keyframes capsuleBounce {
  0%, 100% { transform: translateX(580%) translateY(0); }
  50% { transform: translateX(580%) translateY(-15px); }
}

.hero-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 16px;
  position: relative;
  z-index: 10;
}

@media (min-width: 640px) {
  .hero-container {
    padding: 0 24px;
  }
}

@media (min-width: 1024px) {
  .hero-container {
    padding: 0 32px;
  }
}

.hero-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 48px;
  align-items: center;
}

@media (min-width: 1024px) {
  .hero-grid {
    grid-template-columns: 1.5fr 1fr;
  }
}

.hero-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  position: relative;
  z-index: 10;
  animation: fadeInUp 1s ease-out forwards;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (min-width: 768px) {
  .hero-content {
    gap: 32px;
  }
}

.hero-stats {
  display: flex;
  align-items: center;
  gap: 12px;
}

@media (min-width: 768px) {
  .hero-stats {
    gap: 16px;
  }
}

.hero-avatars {
  display: flex;
  /* margin-left: -8px; */
}

.hero-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: 2px solid white;
  object-fit: cover;
  margin-left: -6px;
}

@media (min-width: 768px) {
  .hero-avatar {
    width: 40px;
    height: 40px;
    margin-left: -8px;
  }
}

.hero-stats-text {
  display: flex;
  flex-direction: column;
}

.hero-stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #111827;
  line-height: 1;
}

.hero-stats-number span {
  font-size: 18px;
  font-weight: normal;
}

.hero-stats-label {
  font-size: 14px;
  color: #6b7280;
}

.hero-title-container {
  margin-bottom: 8px;
}

.hero-title {
  font-size: 32px;
  font-weight: bold;
  color: #0F1C46;
  line-height: 1.1;
  margin: 0;
  animation: slideInLeft 1.2s ease-out 0.3s both;
  background: linear-gradient(135deg, #0F1C46 0%, #1e40af 50%, #0F1C46 100%);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  /* -webkit-text-fill-color: transparent; */
  background-clip: text;
  animation: slideInLeft 1.2s ease-out 0.3s both, gradientShift 4s ease-in-out infinite;
}

.hero-title-faith {
  color: #06b6d4;
  animation: pulse 2s ease-in-out infinite;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes gradientShift {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@media (min-width: 640px) {
  .hero-title {
    font-size: 40px;
  }
}

@media (min-width: 768px) {
  .hero-title {
    font-size: 48px;
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: 60px;
  }
}

@media (min-width: 1280px) {
  .hero-title {
    font-size: 72px;
  }
}

.hero-subtitle-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.hero-subtitle-header {
  display: flex;
  align-items: center;
  gap: 12px;
}

.hero-subtitle-label {
  color: #374151;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.05em;
}

.hero-subtitle-line {
  height: 8px;
  border: 1px solid #9ca3af;
    border-radius: 25px;
  /* background-color: #9ca3af; */
  flex: 1;
  max-width: 120px;
}

.hero-subtitle-text {
  color: #374151;
  font-weight: 600;
  font-size: 14px;
  letter-spacing: 0.025em;
  max-width: 384px;
  line-height: 1.6;
}

.hero-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  animation: fadeInUp 1.5s ease-out 0.8s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (min-width: 640px) {
  .hero-buttons {
    flex-direction: row;
  }
}

.hero-button {
  padding: 16px 32px;
  border-radius: 12px;
  font-weight: 600;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.hero-button-primary {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
  position: relative;
  overflow: hidden;
}

.hero-button-primary::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
  transform: scale(0);
  opacity: 0;
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.hero-button-primary:hover {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(6, 182, 212, 0.4);
}

.hero-button-primary:hover::before {
  transform: scale(1);
  opacity: 1;
  animation: ripple 1.5s ease-out;
}

@keyframes ripple {
  0% { transform: scale(0); opacity: 0.5; }
  100% { transform: scale(1); opacity: 0; }
}

.hero-button-secondary {
  background-color: transparent;
  color: #06b6d4;
  border: 2px solid #06b6d4;
}

.hero-button-secondary:hover {
  background-color: #06b6d4;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(6, 182, 212, 0.3);
}

.hero-image-container {
  position: relative;
}

.hero-image-wrapper {
  position: relative;
  height: 500px;
}

@media (min-width: 1024px) {
  .hero-image-wrapper {
    height: 600px;
  }
}

.hero-image-frame {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 24px;
  overflow: hidden;
  /* box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); */
}

.hero-image {
  width: 100%;
  /* height: 100%; */
  object-fit: cover;
}

.hero-bible-card {
  position: absolute;
  bottom: 25%;
  left: 55%;
  width: 297px;
  height: 130px;
  background: url('../assets/bible-verse-card.png');
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
  padding: 20px 24px;
  /* box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); */
  z-index: 10;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transform: translateX(-50%);
  animation: slideInRight 1.8s ease-out 1.2s both, floatBible 4s ease-in-out infinite 2s;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hero-bible-card:hover {
  transform: translateX(-50%) translateY(-10px) scale(1.05);
  /* box-shadow: 0 35px 70px -12px rgba(0, 0, 0, 0.35); */
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-50%) translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%);
  }
}

@keyframes floatBible {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-8px); }
}

.hero-bible-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-top: 15px;
  padding-left: 20px;
}

.hero-bible-verse {
  color: #1f2937;
  font-family: 'Poppins', sans-serif;
  font-weight: 700;
  font-size: 18px;
  line-height: 1.3;
  margin: 0;
}

.hero-bible-reference {
  color: #374151;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 600;
  margin: 0;
}

.hero-bible-say {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  transform: rotate(0deg);
  color: #fff;
  font-family: 'Poppins', sans-serif;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 1px;
  /* text-transform: uppercase; */
  margin-left: 16px;
  margin-top: 20px;
}

.hero-arrow-button {
  position: absolute;
  top: 50%;
  right: 24px;
  transform: translateY(-50%);
  background-color: white;
  border-radius: 50%;
  padding: 16px;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  z-index: 10;
  border: 1px solid #f3f4f6;
  cursor: pointer;
}

.hero-arrow-button svg {
  width: 24px;
  height: 24px;
  color: #374151;
}

.hero-specialties {
  position: absolute;
  bottom: 20px;
  left: 70px;
  /* right: 24px; */
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 10;
}

.hero-specialties-row {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.hero-specialty-tag {
  /* background-color: rgba(255, 255, 255, 0.95); */
  backdrop-filter: blur(8px);
  padding: 8px 16px;
  border-radius: 9999px;
  font-size: 14px;
  font-weight: 500;
  color: #fff;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.664);
}

.hero-specialty-tag-1{
  /* background-color: #08AEBE; */
  margin-left: 15px;
}

.hero-decorative-1 {
  position: absolute;
  bottom: 35%;
  left: 40%;
  width: 100px;
  height: 100px;
  background-color: #08AEBE;
  border-radius: 50%;
  opacity: 0.6;
  filter: blur(50px);
}

.hero-decorative-2 {
  position: absolute;
  top: -16px;
  right: -16px;
  width: 48px;
  height: 48px;
  background-color: #93c5fd;
  border-radius: 50%;
  opacity: 0.5;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .hero-bible-card {
    width: 250px;
    height: 110px;
    padding: 16px 20px;
    bottom: 20%;
  }

  .hero-bible-verse {
    font-size: 16px;
  }

  .hero-bible-reference {
    font-size: 13px;
  }

  .hero-bible-say {
    font-size: 11px;
    margin-left: 12px;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding: 60px 0 40px;
    width: 100%;
    overflow-x: hidden;
  }

  .hero-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .hero-grid {
    grid-template-columns: 1fr;
    gap: 30px;
    /* text-align: center; */
    width: 100%;
  }

  .hero-content {
    order: 1;
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .hero-title {
    font-size: 28px;
    line-height: 1.3;
    margin-bottom: 16px;
    text-align: center;
    font-weight: 700;
    color: #0F1C46;
    word-spacing: 2px;
  }

  .hero-title-faith {
    background: linear-gradient(135deg, #06b6d4, #0891b2);
    color: white;
    padding: 6px 14px;
    border-radius: 8px;
    margin: 0 2px;
    display: inline;
    font-size: inherit;
    font-weight: inherit;
    white-space: nowrap;
  }

  .hero-image-container {
    order: 2;
    width: 100%;
    position: relative;
    margin-top: 32px;
  }

  .hero-image-wrapper {
    position: relative;
    height: 400px;
    border-radius: 20px;
    overflow: hidden;
  }

  .hero-image-frame {
    width: 100%;
    height: 100%;
    border-radius: 20px;
    overflow: hidden;
  }

  .hero-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .hero-bible-card {
    position: absolute;
    /* top: 20px; */
    /* right: 200px !important; */
    bottom: 28%;
    width: 200px;
    height: 90px;
    padding: 12px 16px;
    z-index: 15;
    border-radius: 12px;
    /* backdrop-filter: blur(10px); */
  }

  .hero-bible-content{
    padding-left: 8px;
  }

  .hero-bible-verse {
    font-size: 15px;
  }

  .hero-bible-reference {
    font-size: 12px;
  }

  .hero-bible-say {
    font-size: 6px;
    margin-left: 10px;
  }

  .hero-stats {
    display: flex;
    width: fit-content;
    /* flex-direction: column; */
    align-items: center;
    gap: 16px;
    margin: 0 auto;
    margin-bottom: 24px;
    padding: 0px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 16px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .hero-avatars {
    display: flex;
    justify-content: center;
    margin-bottom: 8px;
  }

  .hero-stats-text {
    text-align: center;
  }

  .hero-stats-number {
    font-size: 24px;
    font-weight: 700;
    color: #06b6d4;
    margin-bottom: 4px;
  }

  .hero-stats-label {
    font-size: 13px;
    color: #6b7280;
    font-weight: 500;
  }

  .hero-subtitle-container {
    margin-bottom: 20px;
  }

  .hero-subtitle-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    /* margin-bottom: 12px; */
  }

  .hero-subtitle-label {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    letter-spacing: 0.05em;
  }

  .hero-subtitle-line {
    height: 8px;
    /* background-color: #9ca3af; */
    border: 1px solid #9ca3af;
    border-radius: 25px;
    flex: 1;
    max-width: 60px;
  }

  .hero-subtitle-text {
    font-size: 15px;
    line-height: 1.5;
    color: #374151;
    font-weight: 600;
    text-align: center;
    max-width: 100%;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 16px;
    align-items: center;
    width: 100%;
    margin-top: 24px;
  }

  .hero-button {
    width: 100%;
    max-width: 320px;
    justify-content: center;
    padding: 16px 24px;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    border-radius: 12px;
    font-weight: 600;
    min-height: 56px;
  }

  .hero-button span {
    display: inline-block;
    white-space: nowrap;
  }

  .hero-specialties {
    position: absolute;
    bottom: 20px;
    left: 0%;
    /* transform: translateX(-50%); */
    display: flex;
    flex-direction: column;
    gap: 12px;
    z-index: 10;
    width: 90%;
  }

  .hero-specialties-row {
    display: flex;
    /* justify-content: center; */
    flex-wrap: wrap;
    gap: 8px;
    margin-left: 20px;
  }

  .hero-specialty-tag {
    /* background-color: rgba(255, 255, 255, 0.95); */
    backdrop-filter: blur(8px);
    color: #fff;
    font-size: 12px;
    font-weight: 600;
    padding: 8px 16px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border: 1px solid rgb(255, 255, 255);
  }

  .hero-specialty-tag-1{
    margin-left: 0px;
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding: 7rem 0 30px;
  }

  .hero-container {
    padding: 0 12px;
  }

  .hero-grid {
    gap: 24px;
  }

  .hero-title {
    font-size: 34px;
    line-height: 1.2;
    font-weight: 700;
    color: #0F1C46;
    word-spacing: 1px;
  }

  .hero-title-faith {
    padding: 4px 10px;
    margin: 0 1px;
    font-size: inherit;
    font-weight: inherit;
    display: inline;
    white-space: nowrap;
  }

  .hero-image-wrapper {
    height: 320px;
  }

  .hero-button {
    max-width: 100%;
    padding: 14px 20px;
    font-size: 15px;
    min-height: 52px;
  }

  .hero-bible-card {
    width: 180px;
    height: 80px;
    padding: 10px 14px;
    /* bottom: 1%; */
    right: 15px;
  }

  .hero-specialty-tag {
    font-size: 11px;
    padding: 6px 12px;
  }

  .hero-subtitle-text {
    font-size: 13px;
  }

  .hero-subtitle-label {
    font-size: 10px;
  }

  .hero-button {
    padding: 12px 16px;
    font-size: 13px;
  }

  .hero-bible-card {
    width: 180px;
    height: 80px;
    padding: 12px 14px;
    /* bottom: 1%; */
  }

  .hero-bible-verse {
    font-size: 13px;
  }

  .hero-bible-reference {
    font-size: 10px;
  }

  .hero-bible-say {
    font-size: 9px;
    margin-left: 8px;
  }

  .hero-specialty-tag {
    font-size: 10px;
    padding: 5px 10px;
  }
}
