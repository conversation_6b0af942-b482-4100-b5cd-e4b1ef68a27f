/* Doctor Finder <PERSON> Styles */
.doctor-finder-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.doctor-finder-modal {
  background: white;
  border-radius: 20px;
  width: 100%;
  max-width: 1200px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

/* Header */
.doctor-finder-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 40px;
  background: linear-gradient(135deg, #F4F7FF 0%, #E8EFFF 100%);
  border-bottom: 1px solid #e5e7eb;
}

.header-content h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 700;
  color: #1a1a1a;
}

.header-content p {
  margin: 0;
  color: #6b7280;
  font-size: 16px;
}

.close-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: background-color 0.2s;
  color: #6b7280;
}

.close-btn:hover {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Search and Filters */
.search-filters-section {
  padding: 30px 40px;
  background: #fafbfc;
  border-bottom: 1px solid #e5e7eb;
}

.search-bar {
  position: relative;
  margin-bottom: 24px;
}

.search-bar svg {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-bar input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: border-color 0.2s;
  background: white;
}

.search-bar input:focus {
  outline: none;
  border-color: #06b6d4;
}

.filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.filter-group select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s;
}

.filter-group select:focus {
  outline: none;
  border-color: #06b6d4;
}

.clear-filters-btn {
  padding: 12px 24px;
  background: transparent;
  color: #06b6d4;
  border: 2px solid #06b6d4;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  height: fit-content;
}

.clear-filters-btn:hover {
  background: #06b6d4;
  color: white;
}

/* Results Section */
.results-section {
  flex: 1;
  overflow-y: auto;
  padding: 30px 40px;
}

.results-header {
  margin-bottom: 24px;
}

.results-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  color: #6b7280;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #f3f4f6;
  border-top: 3px solid #06b6d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Doctors Grid */
.doctors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 24px;
}

/* Doctor Card */
.doctor-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.doctor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #06b6d4;
}

.doctor-card-header {
  display: flex;
  gap: 16px;
  margin-bottom: 20px;
  align-items: flex-start;
}

.doctor-avatar {
  font-size: 48px;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #F4F7FF 0%, #E8EFFF 100%);
  border-radius: 50%;
  flex-shrink: 0;
}

.doctor-basic-info {
  flex: 1;
}

.doctor-basic-info h4 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 700;
  color: #1a1a1a;
}

.specialty {
  margin: 0 0 4px 0;
  color: #06b6d4;
  font-weight: 600;
  font-size: 14px;
}

.department {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.doctor-rating {
  text-align: right;
}

.stars {
  display: flex;
  gap: 2px;
  justify-content: flex-end;
  margin-bottom: 4px;
}

.star-filled {
  color: #fbbf24;
  fill: currentColor;
}

.star-empty {
  color: #d1d5db;
}

.rating-text {
  font-size: 12px;
  color: #6b7280;
}

/* Doctor Details */
.doctor-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #374151;
}

.detail-item svg {
  color: #06b6d4;
  flex-shrink: 0;
}

/* Doctor About */
.doctor-about {
  margin-bottom: 16px;
}

.doctor-about p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.5;
}

/* Availability */
.doctor-availability {
  margin-bottom: 20px;
}

.doctor-availability h5 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.availability-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.availability-tag {
  padding: 4px 8px;
  background: #e0f2fe;
  color: #0891b2;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

/* Doctor Actions */
.doctor-actions {
  display: flex;
  gap: 12px;
}

.book-appointment-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.book-appointment-btn:hover {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  transform: translateY(-1px);
}

.view-profile-btn {
  padding: 12px 20px;
  background: transparent;
  color: #06b6d4;
  border: 2px solid #06b6d4;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.view-profile-btn:hover {
  background: #06b6d4;
  color: white;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 60px 20px;
  color: #6b7280;
}

.no-results-icon {
  font-size: 64px;
  margin-bottom: 20px;
}

.no-results h3 {
  margin: 0 0 12px 0;
  font-size: 24px;
  color: #374151;
}

.no-results p {
  margin: 0 0 24px 0;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .doctor-finder-modal {
    margin: 10px;
    max-height: 95vh;
  }
  
  .doctor-finder-header,
  .search-filters-section,
  .results-section {
    padding: 20px;
  }
  
  .filters-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .doctors-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .doctor-card-header {
    flex-direction: column;
    text-align: center;
  }
  
  .doctor-rating {
    text-align: center;
  }
  
  .doctor-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .doctor-finder-header,
  .search-filters-section,
  .results-section {
    padding: 16px;
  }
  
  .header-content h2 {
    font-size: 24px;
  }
  
  .doctor-card {
    padding: 16px;
  }
  
  .doctor-avatar {
    width: 60px;
    height: 60px;
    font-size: 32px;
  }
}
