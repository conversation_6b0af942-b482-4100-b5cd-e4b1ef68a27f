/* Find Doctor Page Styles */
.find-doctor-page {
  min-height: 100vh;
  background-color: #F4F7FF;
}

.find-doctor-main {
  padding-top: 0;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Page Header */
.page-header {
  background: linear-gradient(135deg, #F4F7FF 0%, #E8EFFF 100%);
  padding: 40px 0;
  border-bottom: 1px solid #e5e7eb;
}

.page-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 16px;
  cursor: pointer;
  padding: 8px 0;
  transition: color 0.2s;
}

.back-button:hover {
  color: #06b6d4;
}

.dashboard-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.dashboard-link:hover {
  background: linear-gradient(135deg, #059669, #047857);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
}

.page-title h1 {
  margin: 0 0 8px 0;
  font-size: 36px;
  font-weight: 700;
  color: #1a1a1a;
}

.page-title p {
  margin: 0;
  color: #6b7280;
  font-size: 18px;
}

/* Search and Filters */
.search-filters-section {
  background: white;
  padding: 40px 0;
  border-bottom: 1px solid #e5e7eb;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-bar {
  position: relative;
  margin-bottom: 32px;
}

.search-bar svg {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.search-bar input {
  width: 100%;
  padding: 18px 20px 18px 56px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: border-color 0.2s;
  background: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.search-bar input:focus {
  outline: none;
  border-color: #06b6d4;
  box-shadow: 0 0 0 3px rgba(6, 182, 212, 0.1);
}

.filters-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.filter-group select {
  padding: 14px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s;
}

.filter-group select:focus {
  outline: none;
  border-color: #06b6d4;
}

.clear-filters-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 24px;
  background: transparent;
  color: #06b6d4;
  border: 2px solid #06b6d4;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  height: fit-content;
}

.clear-filters-btn:hover {
  background: #06b6d4;
  color: white;
}

/* Results Section */
.results-section {
  padding: 40px 0 80px 0;
}

.results-header {
  margin-bottom: 32px;
}

.results-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #6b7280;
}

.loading-spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #06b6d4;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Doctors Grid */
.doctors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 32px;
}

/* Find Doctor Card */
.find-doctor-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 16px;
  padding: 28px;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.find-doctor-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border-color: #06b6d4;
}

.doctor-card-header {
  display: flex;
  gap: 20px;
  margin-bottom: 24px;
  align-items: flex-start;
}

.doctor-avatar {
  font-size: 56px;
  width: 88px;
  height: 88px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #F4F7FF 0%, #E8EFFF 100%);
  border-radius: 50%;
  flex-shrink: 0;
  border: 3px solid #e5e7eb;
  overflow: hidden;
}

.doctor-avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
}

.doctor-avatar-emoji {
  font-size: 56px;
}

.doctor-basic-info {
  flex: 1;
}

.doctor-basic-info h3 {
  margin: 0 0 8px 0;
  font-size: 22px;
  font-weight: 700;
  color: #1a1a1a;
}

.specialty {
  margin: 0 0 4px 0;
  color: #06b6d4;
  font-weight: 600;
  font-size: 16px;
}

.department {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
}

.doctor-rating {
  text-align: right;
}

.stars {
  display: flex;
  gap: 2px;
  justify-content: flex-end;
  margin-bottom: 6px;
}

.star-filled {
  color: #fbbf24;
  fill: currentColor;
}

.star-empty {
  color: #d1d5db;
}

.rating-text {
  font-size: 13px;
  color: #6b7280;
}

/* Doctor Details */
.doctor-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 20px;
  padding: 20px;
  /* background: #f8fafc; */
  border-radius: 12px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #374151;
}

.detail-item svg {
  color: #06b6d4;
  flex-shrink: 0;
}

/* Doctor About */
.doctor-about {
  margin-bottom: 20px;
}

.doctor-about p {
  margin: 0;
  color: #6b7280;
  font-size: 14px;
  line-height: 1.6;
}

/* Availability */
.doctor-availability {
  margin-bottom: 24px;
}

.doctor-availability h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.availability-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.availability-tag {
  padding: 6px 12px;
  background: #e0f2fe;
  color: #0891b2;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

/* Doctor Actions */
.doctor-actions {
  display: flex;
  gap: 12px;
}

.book-appointment-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 14px 20px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border: none;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.book-appointment-btn:hover {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  transform: translateY(-1px);
}

.view-profile-btn {
  padding: 14px 20px;
  background: transparent;
  color: #06b6d4;
  border: 2px solid #06b6d4;
  border-radius: 10px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 14px;
}

.view-profile-btn:hover {
  background: #06b6d4;
  color: white;
}

/* No Results */
.no-results {
  text-align: center;
  padding: 80px 20px;
  color: #6b7280;
}

.no-results-icon {
  font-size: 80px;
  margin-bottom: 24px;
}

.no-results h3 {
  margin: 0 0 12px 0;
  font-size: 28px;
  color: #374151;
}

.no-results p {
  margin: 0 0 32px 0;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .page-header {
    padding: 24px 0;
    width: 100%;
    overflow-x: hidden;
  }

  .page-header-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }
  
  .page-title h1 {
    font-size: 28px;
  }
  
  .search-filters-section {
    padding: 24px 0;
  }
  
  .filters-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .doctors-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .find-doctor-card {
    padding: 20px;
  }

  .doctor-card-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .doctor-rating {
    text-align: center;
  }
  
  .doctor-actions {
    flex-direction: column;
  }
  
  .doctor-avatar {
    width: 72px;
    height: 72px;
    font-size: 40px;
    align-self: center;
  }
}

@media (max-width: 480px) {
  .page-header,
  .search-filters-section,
  .results-section {
    padding: 20px 0;
  }
  
  .page-title h1 {
    font-size: 24px;
  }
  
  .find-doctor-card {
    padding: 16px;
  }
  
  .search-bar input {
    padding: 14px 16px 14px 48px;
  }
}
