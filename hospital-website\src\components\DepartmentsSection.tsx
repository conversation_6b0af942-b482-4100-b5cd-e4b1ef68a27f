import React from "react";
import "./DepartmentsSection.css";

const DepartmentsSection: React.FC = () => {
  return (
    <section className="departments-section">
      <div className="departments-container">
        <div className="departments-grid">
          {/* First Row - 2 cards + merged title section */}
          <div className="departments-card">
            <div className="department-icon">
              <svg
                width="19"
                height="19"
                viewBox="0 0 19 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect x="6.5" width="6" height="19" rx="3" fill="white" />
                <rect
                  y="12.5"
                  width="6"
                  height="19"
                  rx="3"
                  transform="rotate(-90 0 12.5)"
                  fill="white"
                />
              </svg>
            </div>
            <h3 className="department-name">CARDIOLOGY</h3>
            <a href="#" className="department-link">
              Learn More
            </a>
          </div>

          <div className="departments-card">
            <div className="department-icon">
              <svg
                width="19"
                height="19"
                viewBox="0 0 19 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect x="6.5" width="6" height="19" rx="3" fill="white" />
                <rect
                  y="12.5"
                  width="6"
                  height="19"
                  rx="3"
                  transform="rotate(-90 0 12.5)"
                  fill="white"
                />
              </svg>
            </div>
            <h3 className="department-name">ORTHOPEDICS</h3>
            <a href="#" className="department-link">
              Learn More
            </a>
          </div>

          <div className="departments-info">
            <div className="departments-badge">Departments</div>
            <h2 className="departments-title">
              Expert care in every department — serving with skill, compassion,
              and a Christ-centered heart.
            </h2>
          </div>

          {/* Second Row - 3 cards + description section */}
          <div className="departments-card">
            <div className="department-icon">
              <svg
                width="19"
                height="19"
                viewBox="0 0 19 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect x="6.5" width="6" height="19" rx="3" fill="white" />
                <rect
                  y="12.5"
                  width="6"
                  height="19"
                  rx="3"
                  transform="rotate(-90 0 12.5)"
                  fill="white"
                />
              </svg>
            </div>
            <h3 className="department-name">PEDIATRICS</h3>
            <a href="#" className="department-link">
              Learn More
            </a>
          </div>

          <div className="departments-card">
            <div className="department-icon">
              <svg
                width="19"
                height="19"
                viewBox="0 0 19 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect x="6.5" width="6" height="19" rx="3" fill="white" />
                <rect
                  y="12.5"
                  width="6"
                  height="19"
                  rx="3"
                  transform="rotate(-90 0 12.5)"
                  fill="white"
                />
              </svg>
            </div>
            <h3 className="department-name">GYNECOLOGY & MATERNITY</h3>
            <a href="#" className="department-link">
              Learn More
            </a>
          </div>

          <div className="departments-card">
            <div className="department-icon">
              <svg
                width="19"
                height="19"
                viewBox="0 0 19 19"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <rect x="6.5" width="6" height="19" rx="3" fill="white" />
                <rect
                  y="12.5"
                  width="6"
                  height="19"
                  rx="3"
                  transform="rotate(-90 0 12.5)"
                  fill="white"
                />
              </svg>
            </div>
            <h3 className="department-name">NEUROLOGY</h3>
            <a href="#" className="department-link">
              Learn More
            </a>
          </div>

          <div className="departments-info-bottom">
            <p className="departments-description">
              At SDAMC, we offer expert care from heart and bone care to child
              and women's health, our skilled doctors are here to serve you
            </p>
            <button className="departments-arrow-btn">
              <svg
                width="34"
                height="34"
                viewBox="0 0 34 34"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M20.7152 11.0154L22.023 19.3295M20.7152 11.0154L12.4011 12.3231M20.7152 11.0154L12.5412 22.2409"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                />
              </svg>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default DepartmentsSection;
