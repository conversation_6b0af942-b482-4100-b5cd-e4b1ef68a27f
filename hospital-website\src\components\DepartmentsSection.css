.departments-section {
  background-color: #F4F7FF;
  padding: 80px 0;
  position: relative;
}

.departments-section .departments-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.departments-section .departments-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-template-rows: 180px 180px;
  gap: 20px;
  align-items: stretch;
  grid-template-areas:
    "card1 card2 title title"
    "card3 card4 card5 description";
}

.departments-section .departments-card {
  background: #E8EFFF;
  border-radius: 20px;
  padding: 24px;
  text-align: left;
  position: relative;
  transition: all 0.3s ease;
  border: none;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  box-shadow: none;
}

/* Grid area assignments */
.departments-section .department-card:nth-child(1) {
  grid-area: card1;
}

.departments-section .department-card:nth-child(2) {
  grid-area: card2;
}

.departments-section .departments-info {
  grid-area: title;
}

.departments-section .department-card:nth-child(4) {
  grid-area: card3;
}

.departments-section .department-card:nth-child(5) {
  grid-area: card4;
}

.departments-section .department-card:nth-child(6) {
  grid-area: card5;
}

.departments-section .departments-description {
  grid-area: description;
}

.departments-section .department-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.15);
}

.departments-section .department-icon {
  min-width: 40px;
  max-width: 40px;
  min-height: 40px;
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.departments-section .department-icon span {
  color: white;
  font-size: 24px;
  font-weight: 500;
}

.departments-section .department-name {
  font-family: 'Poppins', sans-serif;
  font-size: 20px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.departments-section .department-link {
  color: #000;
  padding: 5px 12px;
  border-radius: 25px;
  background: #fff;
  width: fit-content;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: color 0.3s ease;
  margin-top: auto;
}

.departments-section .department-link:hover {
  color: #06b6d4;
}

/* Title section spans 2 columns in first row */
.departments-section .departments-info {
  /* padding: 20px; */
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  background: transparent;
  height: 100%;
}

.departments-section .departments-badge {
  display: inline-flex;
  align-items: center;
  background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%);
  color: white;
  padding: 8px 20px;
  border-radius: 25px;
  font-family: 'Inter', sans-serif;
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 20px;
  width: fit-content;
}

.departments-section .departments-title {
  font-family: 'Inter', sans-serif;
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  color: #1e293b;
  margin: 0;
}

/* Description section spans 1 column in second row */
.departments-section .departments-info-bottom {
  grid-area: description;
  /* padding: 20px; */
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
}

.departments-section .departments-description {
  font-family: 'Inter', sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: #475569;
  margin-bottom: 20px;
  flex: 1;
}

.departments-section .departments-arrow-btn {
  width: 50px;
  min-height: 50px;
  background-color: #1e293b;
  border-radius: 50px;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  align-self: flex-start;
}

.departments-section .departments-arrow-btn:hover {
  background-color: #06b6d4;
  transform: scale(1.1);
}

.departments-section .departments-arrow-btn span {
  color: white;
  font-size: 20px;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .departments-section .departments-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: auto;
    grid-template-areas:
      "title title"
      "card1 card2"
      "card3 card4"
      "card5 description";
  }

  .departments-section .departments-info {
    text-align: center;
    padding: 30px 0;
  }

  .departments-section .departments-description {
    text-align: center;
    padding: 30px 0;
  }

  .departments-section .departments-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .departments-section {
    padding: 60px 0;
    width: 100%;
    overflow-x: hidden;
  }

  .departments-section .departments-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .departments-section .departments-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
    grid-template-areas:
      "title title"
      "card1 card2"
      "card3 card4"
      "card5 description";
  }

  /* Title section comes first on mobile */
  .departments-section .departments-info {
    text-align: center;
    padding: 0 0 20px 0;
  }

  .departments-badge{
    margin: 0 auto;
  }
  .departments-section .departments-card{
    height: 200px !important;
    padding: 15px;
  }

  /* Show description on mobile */
  .departments-section .departments-description {
    text-align: center;
    padding: 20px 0;
  }

  .departments-section .department-card {
    height: 140px;
    padding: 24px 20px;
  }

  .departments-section .departments-title {
    font-size: 24px;
    margin-bottom: 0;
  }

  .departments-section .departments-description  {
    font-size: 14px;
    text-align: left !important;
    padding: 0px;
  }

  .departments-section .department-icon {
    width: 36px;
    height: 36px;
  }

  .departments-section .department-icon span {
    font-size: 20px;
  }

  .departments-section .department-name {
    font-size: 14px;
    margin-bottom: 12px;
  }

  .departments-section .department-link {
    font-size: 12px;
    min-height: fit-content;
    padding: 8px 20px;
  }

  .departments-section .departments-arrow-btn {
    max-width: 40px;
    min-height: 40px !important;
    max-height: 40px;
    font-size: 16px;
  }

  .departments-section .departments-info-bottom{

  }
}
