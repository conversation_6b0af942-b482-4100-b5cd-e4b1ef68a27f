import React, { useState } from 'react';
import { AuthProvider } from './contexts/AuthContext';
import Header from './components/Header';
import HeroSection from './components/HeroSection';
import HeartBehindCareSection from './components/HeartBehindCareSection';
import AboutSection from './components/AboutSection';
import DepartmentsSection from './components/DepartmentsSection';
import ServicesSection from './components/ServicesSection';
import DoctorsSection from './components/DoctorsSection';
import BlogsSection from './components/BlogsSection';
import TestimonialsSection from './components/TestimonialsSection';
import Footer from './components/Footer';
import DoctorDashboard from './components/DoctorDashboard';
import FindDoctorPage from './pages/FindDoctorPage';
import AboutPage from './pages/AboutPage';
import DoctorPage from './pages/DoctorPage';
import ServicesPage from './pages/ServicesPage';
import DepartmentPage from './pages/DepartmentPage';

function App() {
  const [currentPage, setCurrentPage] = useState('home');

  // Check URL for different pages
  React.useEffect(() => {
    const path = window.location.pathname;
    if (path === '/doctor-dashboard') {
      setCurrentPage('dashboard');
    } else if (path === '/find-doctor') {
      setCurrentPage('find-doctor');
    } else if (path === '/about') {
      setCurrentPage('about');
    } else if (path === '/doctors') {
      setCurrentPage('doctors');
    } else if (path === '/services') {
      setCurrentPage('services');
    } else if (path === '/departments') {
      setCurrentPage('departments');
    } else {
      setCurrentPage('home');
    }
  }, []);

  return (
    <AuthProvider>
      <div style={{ minHeight: '100vh' }}>
        {currentPage === 'dashboard' ? (
          <DoctorDashboard />
        ) : currentPage === 'find-doctor' ? (
          <FindDoctorPage />
        ) : currentPage === 'about' ? (
          <AboutPage />
        ) : currentPage === 'doctors' ? (
          <DoctorPage />
        ) : currentPage === 'services' ? (
          <ServicesPage />
        ) : currentPage === 'departments' ? (
          <DepartmentPage />
        ) : (
          <>
            <Header />
            <HeroSection />
            <AboutSection />
            <HeartBehindCareSection />
            <DepartmentsSection />
            <ServicesSection />
            <DoctorsSection />
            <BlogsSection />
            <TestimonialsSection />
            <Footer />
          </>
        )}
      </div>
    </AuthProvider>
  );
}

export default App;
