/* About Page Styles */
.about-page {
  min-height: 100vh;
  background: #F4F7FF;
}

/* Page Header */
.page-header {
  position: relative;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.page-header-background {
  position: absolute;
  inset: 0;
  background:
    url('../assets/page-header.webp') center center no-repeat,
    url('../assets/header-bg.webp') center top / cover;
  z-index: 1;
}

.page-header-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.page-header-container {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1a1a1a;
}

.page-header-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 1rem;
  color: #666;
}

.breadcrumb-item {
  color: #666;
}

.breadcrumb-item.active {
  color: #1a1a1a;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #999;
}

/* Experience Care Section */
.experience-care-section {
  padding: 100px 0;
  background: white;
}

.experience-care-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.experience-care-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.experience-care-left {
  position: relative;
}

.experience-care-image-wrapper {
  position: relative;
}

.experience-care-dots {
  position: absolute;
  top: -30px;
  left: -30px;
  width: 120px;
  height: 120px;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 120 120"><defs><pattern id="dots" x="0" y="0" width="8" height="8" patternUnits="userSpaceOnUse"><circle cx="4" cy="4" r="1" fill="%23333"/></pattern></defs><rect width="120" height="120" fill="url(%23dots)"/></svg>') repeat;
  z-index: 1;
}

.experience-care-main-image {
  position: relative;
  height: 540px !important;
  z-index: 2;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.experience-care-main-image img {
  width: 100%;
  height: 540px;
  object-fit: cover;
}

.experience-care-small-image {
  position: absolute;
  bottom: -20px;
  left: -40px;
  width: 180px;
  height: 120px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
  z-index: 3;
  border: 4px solid white;
}

.experience-care-small-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.experience-badge {
  position: absolute;
  bottom: 40px;
  right: -30px;
  background: #06b6d4;
  color: white;
  padding: 25px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 15px 30px rgba(6, 182, 212, 0.3);
  z-index: 4;
  min-width: 140px;
}

.experience-badge-number {
  font-size: 2.5rem;
  font-weight: 800;
  line-height: 1;
  margin-bottom: 5px;
}

.experience-badge-text {
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.2;
}

.experience-care-right {
  padding-left: 40px;
}

.experience-care-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 30px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
  padding: 10px 20px;
  background-color: #cccc;
  width: fit-content;
  border-radius: 25px;
}

.experience-care-label {
  /* color: #666; */
  font-weight: 600;
}

.experience-care-about {
  color: #666;
  font-weight: 600;
}

.experience-care-arrow {
  color: #666;
  font-weight: bold;
  font-size: 16px;
}

.experience-care-title {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  color: #1a1a1a;
  margin: 0 0 25px 0;
}

.experience-care-description {
  font-size: 1rem;
  line-height: 1.6;
  color: #666;
  margin-bottom: 40px;
}

.experience-care-stats {
  margin-bottom: 40px;
}

.stat-item {
  /* margin-bottom: 25px; */
}

.stat-label {
  font-size: 0.95rem;
  color: #333;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-bar {
  width: 100%;
  height: 8px;
  background: #e5e7eb;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 5px;
}

.stat-progress {
  height: 100%;
  background: linear-gradient(90deg, #06b6d4, #0891b2);
  border-radius: 4px;
  transition: width 2s ease-in-out;
}

.stat-percentage {
  font-size: 0.9rem;
  color: #333;
  font-weight: 600;
  text-align: right;
}

.schedule-appointment-btn {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border: none;
  padding: 18px 35px;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 10px 25px rgba(6, 182, 212, 0.3);
}

.schedule-appointment-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 35px rgba(6, 182, 212, 0.4);
}

.btn-icon {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .page-header {
    height: 250px;
    width: 100%;
    overflow-x: hidden;
  }

  .page-header-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .page-header-title {
    font-size: 2.5rem;
  }

  .experience-care-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .experience-care-right {
    padding-left: 0;
  }

  .experience-care-title {
    font-size: 2.5rem;
  }

  .experience-care-dots {
    width: 80px;
    height: 80px;
    top: -20px;
    left: -20px;
  }

  .experience-care-small-image {
    width: 140px;
    height: 100px;
    left: -20px;
    bottom: -15px;
  }

  .experience-badge {
    right: -20px;
    bottom: 30px;
    padding: 20px;
    min-width: 120px;
  }

  .experience-badge-number {
    font-size: 2rem;
  }

  .experience-badge-text {
    font-size: 0.8rem;
  }

  .hospital-details-content {
    display: flex !important;
    flex-direction: column;
    grid-template-columns: 1fr;
    gap: 40px;
  }

  .hospital-details-left{
    width: 100%;
  }

  .hospital-details-right {
    padding-left: 0 !important;
  }

  .hospital-details-title {
    font-size: 2.2rem !important;
  }

  .hospital-image img {
    height: 350px;
  }

  .hospital-stats-grid {
    gap: 20px;
  }

  .stat-number {
    font-size: 2rem;
  }
}

/* Mission & Vision */
.mission-vision {
  padding: 120px 0;
  background: #F4F7FF;
}

.mission-vision-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 60px;
  max-width: 1000px;
  margin: 0 auto;
}

.mission-card, .vision-card {
  background: white;
  padding: 60px 40px;
  border-radius: 24px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
  position: relative;
  overflow: hidden;
}

.mission-card::before, .vision-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.mission-card:hover, .vision-card:hover {
  transform: translateY(-10px);
}

.mission-icon, .vision-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #E8EFFF, #F4F7FF);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 30px;
  color: #06b6d4;
}

.mission-card h3, .vision-card h3 {
  font-size: 2rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #1a1a1a;
}

.mission-card p, .vision-card p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #666;
}

/* Hospital Details Section */
.hospital-details-section {
  padding: 80px 0;
  background-color: #ffffff;
}

.hospital-details-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.hospital-details-left {
  position: relative;
}

.hospital-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hospital-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  display: block;
}

.hospital-details-right {
  padding-left: 20px;
}

.hospital-details-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 20px;
}

.hospital-details-label {
  color: #06b6d4;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hospital-details-about {
  color: #06b6d4;
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hospital-details-arrow {
  color: #06b6d4;
  font-size: 1.2rem;
  font-weight: bold;
}

.hospital-details-title {
  font-size: 2.8rem;
  font-weight: 700;
  color: #1e293b;
  line-height: 1.2;
  margin-bottom: 25px;
}

.hospital-details-description {
  color: #64748b;
  font-size: 1rem;
  line-height: 1.7;
  margin-bottom: 40px;
}

.hospital-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.stat-box {
  text-align: left;
}

.hospital-details-section .hospital-stats-grid .stat-box .stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #06b6d4 !important;
  margin-bottom: 5px;
}

.hospital-details-section .stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #06b6d4 !important;
  margin-bottom: 5px;
}

.stat-number {
  font-size: 2.5rem;
  font-weight: 700;
  color: #06b6d4 !important;
  margin-bottom: 5px;
}

.stat-label {
  color: #64748b;
  font-size: 0.9rem;
  font-weight: 500;
  line-height: 1.4;
}

/* Stats Section */
.stats-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.stats-section::before {
  content: '';
  position: absolute;
  inset: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1.5" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></svg>') repeat;
  opacity: 0.3;
}

.stats-header {
  text-align: center;
  margin-bottom: 80px;
  position: relative;
  z-index: 2;
}

.stats-header h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
}

.stats-header p {
  font-size: 1.2rem;
  opacity: 0.9;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  position: relative;
  z-index: 2;
}

.stat-card {
  text-align: center;
  padding: 40px 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-10px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 20px;
}



.stat-label {
  font-size: 1.1rem;
  font-weight: 500;
  opacity: 0.9;
}

/* Animation for stats */
.animate-stats .stat-number {
  animation: countUp 2s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Timeline Section - Award Winning Design */
.timeline-section {
  padding: 120px 0;
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow: hidden;
}

.timeline-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(6, 182, 212, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 60%, rgba(236, 72, 153, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.timeline-header {
  text-align: center;
  margin-bottom: 100px;
  position: relative;
  z-index: 2;
}

.timeline-header h2 {
  font-size: 4rem;
  font-weight: 800;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #06b6d4, #8b5cf6, #ec4899);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.timeline-header p {
  font-size: 1.3rem;
  color: rgba(255, 255, 255, 0.7);
  max-width: 600px;
  margin: 0 auto;
}

.timeline-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.timeline-line {
  position: absolute;
  left: 50%;
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg,
    transparent 0%,
    #06b6d4 10%,
    #8b5cf6 50%,
    #ec4899 90%,
    transparent 100%);
  transform: translateX(-50%);
  opacity: 0;
}

.timeline-progress {
  position: absolute;
  left: 50%;
  top: 0;
  width: 4px;
  background: linear-gradient(180deg, #06b6d4, #8b5cf6, #ec4899);
  transform: translateX(-50%);
  height: 0%;
  border-radius: 2px;
  box-shadow: 0 0 20px rgba(6, 182, 212, 0.5);
}

.timeline-item {
  position: relative;
  margin-bottom: 120px;
  opacity: 0;
  transform: translateY(100px);
}

.timeline-item:nth-child(odd) {
  padding-right: 60%;
}

.timeline-item:nth-child(even) {
  padding-left: 60%;
  text-align: right;
}

.timeline-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 24px;
  padding: 40px;
  position: relative;
  overflow: hidden;
  transition: all 0.4s ease;
}

.timeline-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(6, 182, 212, 0.1) 0%,
    rgba(139, 92, 246, 0.1) 50%,
    rgba(236, 72, 153, 0.1) 100%);
  opacity: 0;
  transition: opacity 0.4s ease;
}

.timeline-card:hover::before {
  opacity: 1;
}

.timeline-card:hover {
  transform: translateY(-10px);
  border-color: rgba(6, 182, 212, 0.3);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.timeline-year {
  font-size: 5rem;
  font-weight: 900;
  background: linear-gradient(135deg, #06b6d4, #8b5cf6);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 20px;
  line-height: 1;
  position: relative;
  z-index: 2;
}

.timeline-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: white;
  margin-bottom: 20px;
  position: relative;
  z-index: 2;
}

.timeline-content p {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.7;
  font-size: 1.1rem;
  position: relative;
  z-index: 2;
}

.timeline-dot {
  position: absolute;
  top: 50%;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #06b6d4, #8b5cf6);
  border: 4px solid rgba(15, 15, 35, 1);
  box-shadow: 0 0 30px rgba(6, 182, 212, 0.6);
  transform: translateY(-50%);
  z-index: 3;
}

.timeline-item:nth-child(odd) .timeline-dot {
  right: -62px;
}

.timeline-item:nth-child(even) .timeline-dot {
  left: -62px;
}

.timeline-floating-element {
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.1), rgba(139, 92, 246, 0.1));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.timeline-floating-element:nth-child(1) {
  top: 10%;
  right: 10%;
  animation-delay: 0s;
}

.timeline-floating-element:nth-child(2) {
  top: 60%;
  left: 5%;
  animation-delay: 2s;
}

.timeline-floating-element:nth-child(3) {
  bottom: 20%;
  right: 15%;
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* Mobile Responsiveness for Timeline */
@media (max-width: 768px) {
  .timeline-header h2 {
    font-size: 2.5rem;
  }

  .timeline-line,
  .timeline-progress {
    left: 30px;
    transform: none;
  }

  .timeline-item:nth-child(odd),
  .timeline-item:nth-child(even) {
    padding-left: 80px;
    padding-right: 20px;
    text-align: left;
  }

  .timeline-item:nth-child(odd) .timeline-dot,
  .timeline-item:nth-child(even) .timeline-dot {
    left: 18px;
    right: auto;
  }

  .timeline-year {
    font-size: 3rem;
  }

  .timeline-content h3 {
    font-size: 1.5rem;
  }

  .timeline-floating-element {
    display: none;
  }
}

/* Values Section */
.values-section {
  padding: 120px 0;
  background: #ffffff;
  overflow: hidden;
}

.values-header {
  text-align: center;
  margin-bottom: 80px;
}

.values-header h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #1a1a1a;
}

.values-header p {
  font-size: 1.2rem;
  color: #666;
}

.values-container {
  display: flex;
  gap: 20px;
  height: 500px;
  /* border-radius: 20px; */
  /* overflow: hidden; */
}

.value-card {
  flex: 1;
  position: relative;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 20px;
  border-radius: 25px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.value-card:hover {
  flex: 2.5;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.value-card:nth-child(1) {
  background: linear-gradient(135deg, #e8f4fd, #d1e9f6);
}

.value-card:nth-child(2) {
  background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
}

.value-card:nth-child(3) {
  background: linear-gradient(135deg, #f3e8ff, #e9d5ff);
}

.value-card:nth-child(4) {
  background: linear-gradient(135deg, #fef3e2, #fed7aa);
}

.value-number {
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a1a1a;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.value-content {
  text-align: center;
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.4s ease 0.2s;
  max-width: 300px;
}

.value-card:hover .value-content {
  opacity: 1;
  transform: translateY(0);
}

.value-card:hover .value-number {
  top: 20px;
  /* background: #06b6d4; */
  /* color: white; */
}

.value-title {
  font-size: 1.8rem;
  font-weight: 700;
  color: #1a1a1a;
  margin: 60px 0 20px 0;
}

.value-description {
  font-size: 1rem;
  color: #666;
  line-height: 1.6;
  margin-bottom: 30px;
}

.value-image {
  width: 100%;
  max-width: 250px;
  height: 150px;
  border-radius: 12px;
  object-fit: cover;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.value-side-text {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%) rotate(-90deg);
  font-size: 1.1rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 2px;
  opacity: 1;
  transition: opacity 0.3s ease;
  white-space: nowrap;
}

.value-card:hover .value-side-text {
  opacity: 0;
}

/* Mobile Responsiveness for Values Section */
@media (max-width: 768px) {
  .values-container {
    flex-direction: column;
    height: auto;
  }

  .value-card {
    flex: none;
    height: 120px;
    transition: all 0.4s ease;
  }

  .value-card:hover {
    flex: none;
    height: 400px;
  }

  .value-side-text {
    transform: translateY(-50%) rotate(0deg);
    left: 50%;
    transform: translateX(-50%);
    top: 80px;
    font-size: 0.8rem;
  }

  .value-number {
    top: 20px;
  }

  .value-card:hover .value-number {
    top: 15px;
  }
}

/* Leadership Section */
.leadership-section {
  padding: 120px 0;
  background: white;
}

.leadership-header {
  text-align: center;
  margin-bottom: 80px;
}

.leadership-header h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #1a1a1a;
}

.leadership-header p {
  font-size: 1.2rem;
  color: #666;
}

.leadership-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.leadership-text h3 {
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 30px;
  color: #1a1a1a;
}

.leadership-text p {
  font-size: 1.1rem;
  line-height: 1.7;
  color: #666;
  margin-bottom: 40px;
}

.leadership-stats {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.leadership-stat {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 25px;
  background: #F4F7FF;
  border-radius: 16px;
  border-left: 4px solid #06b6d4;
}

.leadership-stat .stat-icon {
  font-size: 2rem;
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.leadership-stat strong {
  font-size: 1.2rem;
  color: #1a1a1a;
  display: block;
  margin-bottom: 5px;
}

.leadership-stat p {
  color: #666;
  margin: 0;
  font-size: 0.95rem;
}

.leadership-image {
  position: relative;
}

.leadership-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  padding: 50px;
  color: white;
  position: relative;
  overflow: hidden;
}

.leadership-card::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1.5" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1" fill="white" opacity="0.1"/></svg>') repeat;
  opacity: 0.3;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

.leadership-quote {
  position: relative;
  z-index: 2;
}

.leadership-quote blockquote {
  font-size: 1.2rem;
  line-height: 1.7;
  font-style: italic;
  margin: 0;
  position: relative;
}

.leadership-quote blockquote::before {
  content: '"';
  font-size: 4rem;
  color: rgba(255, 255, 255, 0.3);
  position: absolute;
  top: -20px;
  left: -20px;
  font-family: serif;
}

/* CTA Section */
.about-cta {
  padding: 120px 0;
  background: linear-gradient(135deg, #E8EFFF, #F4F7FF);
  text-align: center;
}

.about-cta-content h2 {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 20px;
  color: #1a1a1a;
}

.about-cta-content p {
  font-size: 1.2rem;
  color: #666;
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  padding: 20px 40px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border: none;
  border-radius: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.cta-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 40px rgba(6, 182, 212, 0.4);
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
  .about-hero-title {
    font-size: 2.5rem;
  }
  
  .about-hero-subtitle {
    font-size: 1.1rem;
  }
  
  .about-hero-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .mission-vision-grid {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .mission-card, .vision-card {
    padding: 40px 30px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }
  
  .timeline::before {
    left: 30px;
  }
  
  .timeline-item {
    width: 100%;
    left: 0 !important;
    padding-left: 80px !important;
    padding-right: 0 !important;
  }
  
  .timeline-item .timeline-content::after {
    display: none;
  }
  
  .timeline-dot {
    left: 20px !important;
    right: auto !important;
  }
  
  .values-grid {
    grid-template-columns: 1fr;
  }
  
  .about-cta-content h2 {
    font-size: 2.5rem;
  }

  .leadership-content {
    grid-template-columns: 1fr;
    gap: 60px;
  }

  .leadership-text h3 {
    font-size: 2rem;
  }

  .leadership-stats {
    gap: 20px;
  }

  .leadership-stat {
    padding: 20px;
  }

  .leadership-card {
    padding: 40px 30px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .timeline-item {
    padding-left: 60px !important;
  }
}
