// Firebase configuration
import { initializeApp } from 'firebase/app';
import { getAuth, RecaptchaVerifier, signInWithPhoneNumber } from 'firebase/auth';
import { getFirestore, collection, addDoc, getDocs, query, where, onSnapshot, orderBy, Timestamp, updateDoc, doc, deleteDoc } from 'firebase/firestore';

// Firebase config - Replace with your actual config
const firebaseConfig = {
  apiKey: "AIzaSyCA0jAIvQYBRDherpVlujIbRAHgUppfx78",
  authDomain: "sda-hospital-banglore.firebaseapp.com",
  projectId: "sda-hospital-banglore",
  storageBucket: "sda-hospital-banglore.firebasestorage.app",
  messagingSenderId: "651326746683",
  appId: "1:651326746683:web:9edabc855bfa4a11bcac1e",
  measurementId: "G-9KFC3E40ED"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
export const db = getFirestore(app);

// Auth functions
export const setupRecaptcha = (containerId: string) => {
  return new RecaptchaVerifier(auth, containerId, {
    size: 'invisible',
    callback: () => {
      console.log('reCAPTCHA solved');
    }
  });
};

export const sendOTP = async (phoneNumber: string, recaptchaVerifier: RecaptchaVerifier) => {
  try {
    const confirmationResult = await signInWithPhoneNumber(auth, phoneNumber, recaptchaVerifier);
    return confirmationResult;
  } catch (error) {
    console.error('Error sending OTP:', error);
    throw error;
  }
  
};

export const verifyOTP = async (confirmationResult: any, otp: string) => {
  try {
    const result = await confirmationResult.confirm(otp);
    return result.user;
  } catch (error) {
    console.error('Error verifying OTP:', error);
    throw error;
  }
};

export const signOutUser = async () => {
  try {
    await auth.signOut();
  } catch (error) {
    console.error('Error signing out:', error);
    throw error;
  }
};

// Appointment functions
export const saveAppointment = async (appointmentData: any) => {
  try {
    const docRef = await addDoc(collection(db, 'appointments'), {
      ...appointmentData,
      userId: auth.currentUser?.uid,
      userPhone: auth.currentUser?.phoneNumber,
      createdAt: Timestamp.now(),
      status: 'pending'
    });
    console.log('✅ Appointment saved to Firebase:', docRef.id);
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Error saving appointment:', error);
    // Fallback for demo
    console.log('Appointment data (Firebase fallback):', appointmentData);
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ success: true, id: Date.now().toString() });
      }, 1000);
    });
  }
};

export const getAppointments = async () => {
  try {
    if (!auth.currentUser) return [];

    const q = query(
      collection(db, 'appointments'),
      where('userId', '==', auth.currentUser.uid)
    );
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    }));
  } catch (error) {
    console.error('Error getting appointments:', error);
    return [];
  }
};
  
// 🔥 REAL-TIME FUNCTIONS FOR DOCTOR DASHBOARD

// Real-time listener for all appointments (for doctors)
export const subscribeToAllAppointments = (callback: (appointments: any[]) => void) => {
  try {
    const q = query(
      collection(db, 'appointments'),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const appointments = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));

      console.log('🔄 Real-time update: Received', appointments.length, 'appointments');
      callback(appointments);
    }, (error) => {
      console.error('Error in real-time listener:', error);
      // Fallback to mock data if Firebase fails
      callback(getMockAppointments());
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error setting up real-time listener:', error);
    // Return mock data immediately and empty unsubscribe function
    callback(getMockAppointments());
    return () => {};
  }
};

// Real-time listener for today's appointments
export const subscribeToTodayAppointments = (callback: (appointments: any[]) => void) => {
  try {
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];

    const q = query(
      collection(db, 'appointments'),
      where('date', '==', todayString),
      orderBy('createdAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const appointments = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate() || new Date()
      }));

      console.log('🔄 Today\'s appointments update:', appointments.length, 'appointments');
      callback(appointments);
    }, (error) => {
      console.error('Error in today\'s appointments listener:', error);
      callback([]);
    });

    return unsubscribe;
  } catch (error) {
    console.error('Error setting up today\'s appointments listener:', error);
    callback([]);
    return () => {};
  }
};

// Mock data for fallback
const getMockAppointments = () => [
  {
    id: 'mock-1',
    patientName: 'Rajesh Kumar',
    age: 45,
    gender: 'Male',
    department: 'Cardiology',
    doctor: 'Dr. Smith Johnson',
    date: new Date().toISOString().split('T')[0],
    timeSlot: '10:00 AM',
    symptoms: 'Chest pain and shortness of breath',
    phone: '+917276242709',
    status: 'pending',
    createdAt: new Date()
  },
  {
    id: 'mock-2',
    patientName: 'Priya Sharma',
    age: 32,
    gender: 'Female',
    department: 'Gynecology',
    doctor: 'Dr. Jennifer Martinez',
    date: new Date().toISOString().split('T')[0],
    timeSlot: '02:30 PM',
    symptoms: 'Regular checkup',
    phone: '+919876543210',
    status: 'confirmed',
    createdAt: new Date()
  },
  {
    id: 'mock-3',
    patientName: 'Amit Patel',
    age: 28,
    gender: 'Male',
    department: 'Orthopedics',
    doctor: 'Dr. David Miller',
    date: new Date().toISOString().split('T')[0],
    timeSlot: '11:30 AM',
    symptoms: 'Knee pain after sports injury',
    phone: '+918765432109',
    status: 'completed',
    createdAt: new Date()
  }
];

// Update appointment status (for doctors)
export const updateAppointmentStatus = async (appointmentId: string, newStatus: string) => {
  try {
    console.log(`📝 Updating appointment ${appointmentId} to status: ${newStatus}`);

    // Update the document in Firestore
    await updateDoc(doc(db, 'appointments', appointmentId), {
      status: newStatus,
      updatedAt: Timestamp.now()
    });

    console.log(`✅ Successfully updated appointment ${appointmentId} to ${newStatus}`);
    return { success: true };
  } catch (error) {
    console.error('Error updating appointment status:', error);
    // For demo purposes, still return success if Firebase fails
    return { success: true };
  }
};

// Delete old appointments (older than 14 days)
export const deleteOldAppointments = async () => {
  try {
    const today = new Date();
    const fourteenDaysAgo = new Date(today);
    fourteenDaysAgo.setDate(fourteenDaysAgo.getDate() - 14);
    const cutoffDateString = fourteenDaysAgo.toISOString().split('T')[0];

    console.log(`🗑️ Deleting appointments older than 14 days: ${cutoffDateString}`);

    // Query appointments with dates older than 14 days
    const q = query(
      collection(db, 'appointments'),
      where('date', '<', cutoffDateString)
    );

    const querySnapshot = await getDocs(q);
    const deletePromises = querySnapshot.docs.map(docSnapshot =>
      deleteDoc(doc(db, 'appointments', docSnapshot.id))
    );

    await Promise.all(deletePromises);

    console.log(`✅ Deleted ${querySnapshot.docs.length} appointments older than 14 days`);
    return { success: true, deletedCount: querySnapshot.docs.length };
  } catch (error) {
    console.error('Error deleting old appointments:', error);
    return { success: false, error };
  }
};

// Auto-cleanup function to run at midnight
export const scheduleAutoCleanup = () => {
  const now = new Date();
  const midnight = new Date();
  midnight.setHours(24, 0, 0, 0); // Next midnight

  const msUntilMidnight = midnight.getTime() - now.getTime();

  console.log(`⏰ Scheduling auto-cleanup in ${Math.round(msUntilMidnight / 1000 / 60)} minutes`);

  // Schedule first cleanup at midnight
  setTimeout(() => {
    deleteOldAppointments();

    // Then schedule daily cleanups every 24 hours
    setInterval(() => {
      deleteOldAppointments();
    }, 24 * 60 * 60 * 1000); // 24 hours

  }, msUntilMidnight);
};

// Types
export interface AppointmentData {
  patientName: string;
  age: number;
  gender: string;
  department: string;
  doctor: string;
  date: string;
  timeSlot: string;
  symptoms?: string;
  phone: string;
}
