.phone-auth-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 20px;
}

.phone-auth-modal {
  background: white;
  border-radius: 16px;
  padding: 32px;
  max-width: 400px;
  width: 100%;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  position: relative;
}

.phone-auth-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.phone-auth-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
}

.phone-auth-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.phone-auth-close:hover {
  background-color: #f5f5f5;
}

.phone-auth-form {
  width: 100%;
}

.phone-auth-step h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.phone-auth-step p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 14px;
}

.phone-input-group {
  display: flex;
  align-items: center;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 16px;
  transition: border-color 0.2s;
}

.phone-input-group:focus-within {
  border-color: #06b6d4;
}

.country-code {
  background: #f8fafc;
  padding: 12px 16px;
  font-weight: 500;
  color: #374151;
  border-right: 1px solid #e5e7eb;
}

.phone-input {
  flex: 1;
  padding: 12px 16px;
  border: none;
  outline: none;
  font-size: 16px;
  background: white;
}

.phone-input::placeholder {
  color: #9ca3af;
}

.otp-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 18px;
  text-align: center;
  letter-spacing: 4px;
  margin-bottom: 16px;
  transition: border-color 0.2s;
}

.otp-input:focus {
  outline: none;
  border-color: #06b6d4;
}

.auth-button {
  width: 100%;
  padding: 12px 24px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 12px;
}

.auth-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  transform: translateY(-1px);
}

.auth-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.auth-button-secondary {
  width: 100%;
  padding: 12px 24px;
  background: transparent;
  color: #06b6d4;
  border: 2px solid #06b6d4;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.auth-button-secondary:hover {
  background: #06b6d4;
  color: white;
}

.auth-error {
  background: #fef2f2;
  color: #dc2626;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  margin-bottom: 16px;
  border: 1px solid #fecaca;
}

#recaptcha-container {
  margin-top: 16px;
}

/* Mobile responsiveness */
@media (max-width: 480px) {
  .phone-auth-overlay {
    padding: 16px;
  }
  
  .phone-auth-modal {
    padding: 24px;
  }
  
  .phone-auth-header h2 {
    font-size: 20px;
  }
  
  .phone-auth-step h3 {
    font-size: 18px;
  }
  
  .phone-input,
  .otp-input,
  .auth-button,
  .auth-button-secondary {
    font-size: 14px;
  }
}
