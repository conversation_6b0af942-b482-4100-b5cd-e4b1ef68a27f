import React from 'react';
import './DoctorsSection.css';

const DoctorsSection: React.FC = () => {
  // Real doctors from SDA Medical Centre website
  const doctors = [
    {
      name: 'Dr. <PERSON>',
      specialty: 'Medical Director',
      experience: '20+ Years',
      rating: 4.9,
      reviews: 450,
      education: 'MD, Medical Administration',
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/<PERSON><PERSON>-670x500.jpg',
      availability: 'Mon - Fri',
      location: 'Administration Department',
      description: 'Experienced medical director overseeing clinical operations and healthcare management with a focus on quality patient care.'
    },
    {
      name: 'Dr. <PERSON><PERSON><PERSON>',
      specialty: 'Obstetrician & Gynaecologist',
      experience: '12+ Years',
      rating: 4.8,
      reviews: 340,
      education: 'MD OBG',
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2024/03/Untitled-design-2-670x500.jpg',
      availability: 'Mon - Sat',
      location: 'Obstetrics & Gynaecology Department',
      description: 'Specialized in pregnancy care, gynecological surgery, and comprehensive women\'s health services.'
    },
    {
      name: 'Dr. <PERSON><PERSON>',
      specialty: 'Pediatrics',
      experience: '15+ Years',
      rating: 4.9,
      reviews: 380,
      education: 'MD Pediatrics',
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2024/09/dr.-sunil-e1726045288897.png',
      availability: 'Mon - Fri',
      location: 'Pediatrics Department',
      description: 'Expert cardiologist specializing in interventional cardiology, heart disease management, and cardiac procedures.'
    },
    {
      name: 'Dr. Sunil Abraham Ninan',
      specialty: 'Pediatrician',
      experience: '15+ Years',
      rating: 4.8,
      reviews: 420,
      education: 'MD Pediatrics',
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Sunil-Abraham-Ninan-670x500.jpg',
      availability: 'Mon - Sat',
      location: 'Pediatrics Department',
      description: 'Dedicated pediatrician providing comprehensive child healthcare, growth monitoring, and pediatric emergency care.'
    },
    {
      name: 'Dr. K.G. Mathew',
      specialty: 'General & Laparoscopic Surgeon',
      experience: '16+ Years',
      rating: 4.7,
      reviews: 290,
      education: 'MS General Surgery',
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/KG-Mathew-670x500.jpg',
      availability: 'Tue - Sat',
      location: 'Surgery Department',
      description: 'Skilled surgeon specializing in minimally invasive laparoscopic procedures and general surgical interventions.'
    },
    {
      name: 'Dr. N S Prakash',
      specialty: 'General Medicine',
      experience: '14+ Years',
      rating: 4.7,
      reviews: 310,
      education: 'MD General Medicine',
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/NS-Prakash-670x500.jpg',
      availability: 'Mon - Sat',
      location: 'General Medicine Department',
      description: 'Experienced physician providing comprehensive internal medicine care with expertise in diabetes and hypertension management.'
    }
  ];

  return (
    <section id="doctors" className="doctors-section">
      <div className="doctors-container">
        {/* Section Header */}
        <div className="doctors-header">
          <div className="doctors-badge">
            <span className="doctors-badge-text">
              Our Doctors
            </span>
          </div>
          <h2 className="doctors-title">
            Meet our expert <span className="doctors-title-highlight">Doctor</span> 
          </h2>
          <p className="doctors-description">
            Our dedicated team of experienced physicians and specialists are committed to providing exceptional healthcare with compassion, expertise, and personalized care for every patient.
          </p>
        </div>

        {/* Doctors Grid */}
        <div className="doctors-grid">
          {doctors.slice(0, 3).map((doctor, index) => (
            <div
              key={index}
              className="doctor-card"
            >
              {/* Doctor Image */}
              <div className="doctor-image">
                <img
                  src={doctor.image}
                  alt={doctor.name}
                  className="doctor-image-photo"
                  onError={(e) => {
                    // Fallback to placeholder if image fails to load
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const placeholder = target.nextElementSibling as HTMLElement;
                    if (placeholder) placeholder.style.display = 'flex';
                  }}
                />
                <div className="doctor-image-placeholder" style={{ display: 'none' }}>
                  <div className="doctor-image-icon">
                    <span>👨‍⚕️</span>
                  </div>
                </div>

                {/* Rating Badge */}
                <div className="doctor-rating-badge">
                  <span className="doctor-rating-star">⭐</span>
                  <span className="doctor-rating-text">{doctor.rating}</span>
                </div>
              </div>

              {/* Doctor Info */}
              <div className="doctor-info">
                <h3 className="doctor-name">
                  {doctor.name}
                </h3>
                <p className="doctor-specialty">
                  {doctor.specialty}
                </p>
                <p className="doctor-experience">
                  {doctor.experience} Experience
                </p>
                <p className="doctor-education">
                  {doctor.education}
                </p>
                
              </div>
            </div>
          ))}
        </div>

        {/* View All Doctors */}
        <div className="doctors-view-all">
          <button className="doctors-view-all-btn">
            View All Doctors
          </button>
        </div>

        {/* Stats Section */}
        
      </div>
    </section>
  );
};

export default DoctorsSection;
