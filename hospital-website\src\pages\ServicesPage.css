/* Services Page Styles */
.services-page {
  min-height: 100vh;
  background: #F4F7FF;
}

/* Page Header - Exact same as <PERSON> and <PERSON> Pages */
.page-header {
  position: relative;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.page-header-background {
  position: absolute;
  inset: 0;
  background:
    url('../assets/page-header.webp') center center no-repeat,
    url('../assets/header-bg.webp') center top / cover;
  z-index: 1;
}

.page-header-overlay {
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0.2);
  z-index: 2;
}

.page-header-container {
  position: relative;
  z-index: 3;
  text-align: center;
  color: white;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.page-header-title {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 16px;
  color: #1a1a1a;
}

.page-header-breadcrumb {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 1rem;
  color: #6b7280;
}

.breadcrumb-item {
  color: #6b7280;
  text-decoration: none;
  transition: color 0.3s ease;
}

.breadcrumb-item.active {
  color: #1a1a1a;
  font-weight: 600;
}

.breadcrumb-separator {
  color: #9ca3af;
}

/* Main Services Section */
.services-main {
  padding: 80px 0;
}

.services-filter {
  text-align: center;
  margin-bottom: 64px;
}

.services-section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 32px;
  font-family: 'Poppins', sans-serif;
}

.services-categories {
  display: flex;
  justify-content: center;
  gap: 16px;
  flex-wrap: wrap;
}

.services-category-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 50px;
  font-weight: 600;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.3s ease;
}

.services-category-btn:hover {
  border-color: #06b6d4;
  color: #06b6d4;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.15);
}

.services-category-btn.active {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  border-color: #06b6d4;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

/* Services Grid */
.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
  gap: 32px;
  margin-top: 48px;
}

.service-card {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s ease;
  opacity: 0;
  transform: translateY(30px);
  animation: slideInUp 0.6s ease forwards;
  border: 1px solid #f0f0f0;
}

@keyframes slideInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.service-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.12);
  border-color: #06b6d4;
}

.service-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
}

.service-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.3);
}

.service-availability {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: #f0f9ff;
  color: #06b6d4;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
}

.service-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1a1a1a;
  margin-bottom: 12px;
  font-family: 'Poppins', sans-serif;
}

.service-description {
  color: #6b7280;
  line-height: 1.6;
  margin-bottom: 24px;
  font-size: 0.95rem;
}

.service-features {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.service-feature {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 0.9rem;
  color: #374151;
}

.service-feature svg {
  color: #10b981;
  flex-shrink: 0;
}

.service-rating {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.service-stars {
  display: flex;
  align-items: center;
  gap: 4px;
}

.service-stars svg {
  color: #d1d5db;
}

.service-stars svg.filled {
  color: #fbbf24;
}

.service-rating-value {
  margin-left: 8px;
  font-weight: 600;
  color: #1a1a1a;
}

.service-reviews {
  font-size: 0.85rem;
  color: #6b7280;
}

.service-actions {
  display: flex;
  gap: 12px;
}

.service-btn-secondary,
.service-btn-primary {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
}

.service-btn-secondary {
  background: #f8fafc;
  color: #64748b;
  border: 2px solid #e2e8f0;
}

.service-btn-secondary:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  transform: translateY(-1px);
}

.service-btn-primary {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
  color: white;
  box-shadow: 0 4px 15px rgba(6, 182, 212, 0.3);
}

.service-btn-primary:hover {
  background: linear-gradient(135deg, #0891b2, #0e7490);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(6, 182, 212, 0.4);
}

/* CTA Section */
.services-cta {
  background: linear-gradient(135deg, #1e293b, #334155);
  padding: 80px 0;
  color: white;
}

.services-cta-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 48px;
}

.services-cta-text h2 {
  font-size: 2.25rem;
  font-weight: 700;
  margin-bottom: 16px;
  font-family: 'Poppins', sans-serif;
}

.services-cta-text p {
  font-size: 1.1rem;
  opacity: 0.9;
  line-height: 1.6;
}

.services-cta-actions {
  display: flex;
  gap: 16px;
  flex-shrink: 0;
}

.services-cta-emergency,
.services-cta-location {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 16px 24px;
  border-radius: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  font-size: 1rem;
}

.services-cta-emergency {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  color: white;
  box-shadow: 0 8px 25px rgba(239, 68, 68, 0.3);
}

.services-cta-emergency:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(239, 68, 68, 0.4);
}

.services-cta-location {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.services-cta-location:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-header {
    height: 250px;
    width: 100%;
    overflow-x: hidden;
  }

  .page-header-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .page-header-title {
    font-size: 2.5rem;
  }

  .services-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }

  .service-card {
    padding: 24px;
  }

  .services-cta-content {
    flex-direction: column;
    text-align: center;
    gap: 32px;
  }

  .services-cta-actions {
    flex-direction: column;
    width: 100%;
  }

  .services-cta-emergency,
  .services-cta-location {
    width: 100%;
    justify-content: center;
  }
}
