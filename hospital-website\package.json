{"name": "hospital-website", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"firebase": "^11.10.0", "gsap": "^3.13.0", "lucide-react": "^0.525.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@vitejs/plugin-react": "^4.5.2", "autoprefixer": "^10.4.21", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.34.1", "vite": "^4.5.0"}}