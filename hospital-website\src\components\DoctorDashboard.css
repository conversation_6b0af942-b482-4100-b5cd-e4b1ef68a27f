/* Modern Professional Doctor Dashboard Styles */
.doctor-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  color: #1e293b;
  font-family: 'Inter', sans-serif;
  position: relative;
  overflow-x: hidden;
}

/* Clean Professional Background */
.dashboard-bg {
  display: none;
}

.bg-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    linear-gradient(rgba(0, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 255, 255, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: gridMove 20s linear infinite;
}

.bg-glow {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 800px;
  height: 800px;
  background: radial-gradient(circle, rgba(0, 255, 255, 0.1) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  animation: pulse 4s ease-in-out infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(50px, 50px); }
}

@keyframes pulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.1); }
}

/* Modern Header */
.dashboard-header {
  position: sticky;
  top: 0;
  z-index: 50;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(148, 163, 184, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-icon {
  font-size: 32px;
  filter: drop-shadow(0 0 10px rgba(0, 255, 255, 0.5));
}

.logo-text h1 {
  margin: 0;
  font-size: 28px;
  font-weight: 800;
  background: linear-gradient(135deg, #0ea5e9, #06b6d4);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.5px;
}

.logo-text span {
  font-size: 11px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 1.5px;
  font-weight: 600;
}

.header-center {
  flex: 1;
  max-width: 500px;
  margin: 0 40px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
  background: #f8fafc;
  border: 1.5px solid #e2e8f0;
  border-radius: 16px;
  padding: 14px 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.search-container:focus-within {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 4px rgba(14, 165, 233, 0.1), 0 1px 3px rgba(0, 0, 0, 0.1);
  background: white;
  transform: translateY(-1px);
}

.search-container svg {
  color: #64748b;
  margin-right: 12px;
  transition: color 0.2s ease;
}

.search-container:focus-within svg {
  color: #0ea5e9;
}

.search-input {
  flex: 1;
  background: none;
  border: none;
  outline: none;
  color: #1e293b;
  font-size: 14px;
  font-weight: 500;
}

.search-input::placeholder {
  color: #94a3b8;
  font-weight: 400;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 15px;
}

/* Real-time Status Indicator */
.realtime-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.realtime-indicator.connected {
  border-color: rgba(46, 213, 115, 0.5);
  background: rgba(46, 213, 115, 0.1);
  color: #2ed573;
}

.realtime-indicator.disconnected {
  border-color: rgba(255, 71, 87, 0.5);
  background: rgba(255, 71, 87, 0.1);
  color: #ff4757;
}

.realtime-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse-dot 2s infinite;
}

@keyframes pulse-dot {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.5; transform: scale(1.2); }
}

/* Refresh Button */
.refresh-btn {
  position: relative;
}

.refresh-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.header-btn {
  position: relative;
  background: #f8fafc;
  border: 1.5px solid #e2e8f0;
  border-radius: 14px;
  padding: 12px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.header-btn:hover {
  background: #0ea5e9;
  border-color: #0ea5e9;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.25);
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
}

.doctor-profile {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 10px 16px;
  background: linear-gradient(135deg, #f8fafc, #f1f5f9);
  border: 1.5px solid #e2e8f0;
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  height: 48px;
  box-sizing: border-box;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.doctor-profile:hover {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border-color: #0ea5e9;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.15);
}

.doctor-avatar {
  font-size: 20px !important;
  width: 30px !important;
  height: 30px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  /* filter: drop-shadow(0 0 5px rgba(0, 255, 255, 0.5)); */
}

.doctor-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
}

.doctor-name {
  font-size: 13px;
  font-weight: 600;
  color: #1f2937;
  line-height: 1.1;
  margin: 0;
}

.doctor-dept {
  font-size: 11px;
  color: #6b7280;
  line-height: 1.1;
  margin: 0;
}

.logout-btn {
  background: rgba(255, 71, 87, 0.2) !important;
  border-color: rgba(255, 71, 87, 0.5) !important;
}

.logout-btn:hover {
  background: rgba(255, 71, 87, 0.3) !important;
  border-color: #ff4757 !important;
  box-shadow: 0 0 15px rgba(255, 71, 87, 0.3) !important;
}

/* Modern Main Content */
.dashboard-main {
  position: relative;
  z-index: 5;
  padding: 32px;
  max-width: 1600px;
  margin: 0 auto;
  background: transparent;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.stat-card {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border: 1.5px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  padding: 28px;
  display: flex;
  align-items: center;
  gap: 24px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* Removed moving gradient animation from stat cards */

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
  border-color: var(--accent-color);
  background: linear-gradient(135deg, #ffffff, #fefefe);
}

.stat-primary { --accent-color: #0ea5e9; --accent-light: #e0f2fe; }
.stat-success { --accent-color: #10b981; --accent-light: #d1fae5; }
.stat-warning { --accent-color: #f59e0b; --accent-light: #fef3c7; }
.stat-info { --accent-color: #8b5cf6; --accent-light: #ede9fe; }

.stat-icon {
  min-width: 64px;
  height: 64px;
  border-radius: 18px;
  background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  position: relative;
}

.stat-icon::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 18px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent);
  pointer-events: none;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 36px;
  font-weight: 800;
  color: #0f172a;
  margin-bottom: 4px;
  line-height: 1;
  letter-spacing: -1px;
}

.stat-label {
  font-size: 13px;
  color: #64748b;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
  line-height: 1.2;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 5px;
  color: var(--accent-color);
  font-size: 12px;
  font-weight: 600;
}

/* Filters Section */
.filters-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 15px;
}

.filter-tabs {
  display: flex;
  gap: 10px;
}

.filter-tab {
  padding: 14px 28px;
  background: #f8fafc;
  border: 1.5px solid #e2e8f0;
  border-radius: 16px;
  color: #64748b;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 600;
  position: relative;
  overflow: hidden;
}

.filter-tab::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, #0ea5e9, #06b6d4);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.filter-tab.active,
.filter-tab:hover {
  color: white;
  border-color: #0ea5e9;
  transform: translateY(-1px);
  box-shadow: 0 8px 25px rgba(14, 165, 233, 0.25);
}

.filter-tab.active::before,
.filter-tab:hover::before {
  opacity: 1;
}

.filter-tab span {
  position: relative;
  z-index: 1;
}

.filter-controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.date-picker,
.status-filter {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8fafc;
  border: 1.5px solid #e2e8f0;
  border-radius: 14px;
  padding: 12px 18px;
  transition: all 0.3s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.date-picker:hover,
.status-filter:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.date-picker svg,
.status-filter svg {
  color: #64748b;
  transition: color 0.2s ease;
}

.date-input,
.status-select {
  background: none;
  border: none;
  outline: none;
  color: #1f2937;
  font-size: 14px;
  cursor: pointer;
}

.date-input::-webkit-calendar-picker-indicator {
  filter: none;
}

/* Appointments Section */
.appointments-section {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  padding: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e5e7eb;
}

.section-title-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.section-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #1f2937;
}

.last-updated {
  font-size: 12px;
  color: #6b7280;
  font-style: italic;
}

.appointment-count {
  background: #dbeafe;
  color: #2563eb;
  padding: 8px 16px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* Appointments Grid */
.appointments-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 25px;
}

.appointment-card {
  background: linear-gradient(135deg, #ffffff, #f8fafc);
  border: 1.5px solid rgba(148, 163, 184, 0.1);
  border-radius: 20px;
  padding: 28px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.appointment-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #0ea5e9, #06b6d4, #0ea5e9);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.appointment-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1), 0 8px 16px rgba(0, 0, 0, 0.06);
  border-color: rgba(14, 165, 233, 0.2);
  background: linear-gradient(135deg, #ffffff, #fefefe);
}

.appointment-card:hover::before {
  opacity: 1;
}

.appointment-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.patient-info {
  display: flex;
  align-items: center;
  gap: 15px;
}

.patient-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, #dbeafe, #bfdbfe);
  border: 2px solid #93c5fd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #2563eb;
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15);
  transition: all 0.3s ease;
}

.patient-avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 6px 16px rgba(37, 99, 235, 0.2);
}

.patient-details h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.patient-meta {
  font-size: 12px;
  color: #6b7280;
}

.appointment-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.status-pending {
  background: rgba(255, 165, 2, 0.2);
  color: #ffa502;
  border: 1px solid rgba(255, 165, 2, 0.3);
}

.status-confirmed {
  background: rgba(46, 213, 115, 0.2);
  color: #2ed573;
  border: 1px solid rgba(46, 213, 115, 0.3);
}

.status-completed {
  background: rgba(55, 66, 250, 0.2);
  color: #3742fa;
  border: 1px solid rgba(55, 66, 250, 0.3);
}

.status-cancelled {
  background: rgba(255, 71, 87, 0.2);
  color: #ff4757;
  border: 1px solid rgba(255, 71, 87, 0.3);
}

.appointment-body {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 20px;
}

.appointment-detail {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4b5563;
}

.appointment-detail svg {
  color: #06b6d4;
  flex-shrink: 0;
}

.appointment-symptoms {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  padding: 15px;
  margin-bottom: 20px;
}

.appointment-symptoms strong {
  color: #06b6d4;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.appointment-symptoms p {
  margin: 8px 0 0 0;
  font-size: 14px;
  color: #4b5563;
  line-height: 1.5;
}

.appointment-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.approve-btn {
  background: #dcfce7;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.approve-btn:hover {
  background: #16a34a;
  color: white;
  box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

.approve-btn:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  border-color: #e5e7eb;
  cursor: not-allowed;
}

.approve-btn:disabled:hover {
  background: #f3f4f6;
  color: #9ca3af;
  box-shadow: none;
  transform: none;
}

.complete-btn {
  background: #dcfce7;
  color: #16a34a;
  border: 1px solid #bbf7d0;
}

.complete-btn:hover {
  background: #16a34a;
  color: white;
  box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3);
}

.complete-btn:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  border-color: #e5e7eb;
  cursor: not-allowed;
}

.complete-btn:disabled:hover {
  background: #f3f4f6;
  color: #9ca3af;
  box-shadow: none;
  transform: none;
}

.cancel-btn {
  background: #fef2f2;
  color: #dc2626;
  border: 1px solid #fecaca;
}

.cancel-btn:hover {
  background: #dc2626;
  color: white;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
}

.cancel-btn:disabled {
  background: #f3f4f6;
  color: #9ca3af;
  border-color: #e5e7eb;
  cursor: not-allowed;
}

.cancel-btn:disabled:hover {
  background: #f3f4f6;
  color: #9ca3af;
  box-shadow: none;
  transform: none;
}

/* Disabled Button States */
.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.action-btn:disabled:hover {
  background: inherit !important;
  box-shadow: none !important;
}

/* Notification Animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.notification {
  animation: slideIn 0.3s ease-out;
}

/* Real-time Update Animations */
.appointment-card.new-appointment {
  animation: newAppointmentGlow 2s ease-out;
}

@keyframes newAppointmentGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 255, 255, 0.7);
    border-color: #00ffff;
  }
  50% {
    box-shadow: 0 0 20px 10px rgba(0, 255, 255, 0.3);
    border-color: #00ffff;
  }
  100% {
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.15);
  }
}

/* Status Update Animation */
.appointment-card.status-updating {
  animation: statusUpdate 0.5s ease-out;
}

@keyframes statusUpdate {
  0% { transform: scale(1); }
  50% { transform: scale(1.02); }
  100% { transform: scale(1); }
}

/* Loading State */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px;
  color: rgba(255, 255, 255, 0.7);
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid #00ffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px;
  /* color: rgba(255, 255, 255, 0.7); */
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  /* filter: drop-shadow(0 0 20px rgba(0, 255, 255, 0.3)); */
}

.empty-state h3 {
  margin: 0 0 10px 0;
  font-size: 24px;
  /* color: #ffffff; */
}

.empty-state p {
  margin: 0;
  font-size: 16px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dashboard-main {
    padding: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .appointments-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 20px;
    padding: 20px;
  }
  
  .header-center {
    margin: 0;
    max-width: 100%;
  }
  
  .filters-section {
    flex-direction: column;
    gap: 20px;
    align-items: stretch;
  }
  
  .filter-controls {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .appointment-body {
    grid-template-columns: 1fr;
  }
  
  .appointment-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .dashboard-main {
    padding: 15px;
  }
  
  .appointments-section {
    padding: 20px;
  }
  
  .appointment-card {
    padding: 20px;
  }
  
  .filter-tabs {
    flex-direction: column;
    width: 100%;
  }
  
  .filter-tab {
    text-align: center;
  }
}
