import React, { useEffect, useRef } from "react";
import { <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, Stethoscope, MapPin, Activity } from "lucide-react";
import "./ServicesSection.css";

const ServicesSection: React.FC = () => {
  const sectionRef = useRef<HTMLElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            entry.target.classList.add("animate-in-view");
            // Animate cards with stagger effect
            if (cardsRef.current) {
              const cards = cardsRef.current.querySelectorAll(".service-card");
              cards.forEach((card, index) => {
                setTimeout(() => {
                  card.classList.add("animate-card");
                }, index * 100);
              });
            }
          }
        });
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  const services = [
    {
      icon: Heart,
      title: "Cardiology",
      description:
        "Comprehensive heart care with advanced cardiac procedures and treatments.",
      features: ["ECG & Echo", "Cardiac Surgery", "Heart Monitoring"],
    },
    {
      icon: Brain,
      title: "Neurology",
      description:
        "Expert neurological care for brain and nervous system disorders.",
      features: ["Brain Imaging", "Stroke Care", "Neurological Surgery"],
    },
    {
      icon: Baby,
      title: "Pediatrics",
      description:
        "Specialized healthcare for infants, children, and adolescents.",
      features: ["Child Wellness", "Vaccinations", "Growth Monitoring"],
    },
    {
      icon: Eye,
      title: "Ophthalmology",
      description:
        "Complete eye care services from routine exams to complex surgeries.",
      features: ["Eye Exams", "Cataract Surgery", "Retinal Care"],
    },
    {
      icon: Bone,
      title: "Orthopedics",
      description: "Treatment for bone, joint, and musculoskeletal conditions.",
      features: ["Joint Replacement", "Sports Medicine", "Fracture Care"],
    },
    {
      icon: Stethoscope,
      title: "General Medicine",
      description:
        "Primary healthcare services for overall health and wellness.",
      features: ["Health Checkups", "Chronic Care", "Preventive Medicine"],
    },
  ];

  return (
    <section id="services" className="services-section" ref={sectionRef}>
      <div className="services-container">
        {/* Section Header */}
        <div className="services-header">
          <div className="services-badge">
            <span className="services-badge-text">Our Services</span>
          </div>
          <h2 className="services-title">
            Comprehensive{" "}
            <span className="services-title-highlight">healthcare</span>{" "}
            services with state-of-the-art technology
          </h2>
          <p className="services-description">
            We provide a full range of medical services with compassionate care,
            ensuring the best possible outcomes for our patients through expert
            treatment and advanced medical technology.
          </p>
        </div>

        {/* Services Grid */}
        <div className="services-grid">
          {services.map((service, index) => {
            const IconComponent = service.icon;
            return (
              <div key={index} className="service-card">
                {/* Icon */}
                <div className="service-icon">
                  <IconComponent size={32} />
                </div>

                {/* Content */}
                <h3 className="service-title">{service.title}</h3>
                <p className="service-description">{service.description}</p>

                {/* Features */}
                <ul className="service-features">
                  {service.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="service-feature">
                      <div className="service-feature-dot"></div>
                      {feature}
                    </li>
                  ))}
                </ul>

                {/* Learn More Link */}
                <div className="service-learn-more">
                  <button className="service-learn-btn">Learn More</button>
                </div>
              </div>
            );
          })}
        </div>

        {/* CTA Section */}
        <section className="services-cta">
          <div className="container-custom">
            <div className="services-cta-content">
              <div className="services-cta-text">
                <h2>Need Immediate Medical Attention?</h2>
                <p>
                  Our emergency department is open 24/7 with expert medical
                  staff ready to help.
                </p>
              </div>
              <div className="services-cta-actions">
                <button className="services-cta-emergency">
                  <Activity size={20} />
                  Emergency: 911
                </button>
                <button className="services-cta-location">
                  <MapPin size={20} />
                  Find Location
                </button>
              </div>
            </div>
          </div>
        </section>
      </div>
    </section>
  );
};

export default ServicesSection;
