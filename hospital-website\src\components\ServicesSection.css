.services-section {
  padding: 60px 0;
  background-color: #F4F7FF;
  position: relative;
  overflow: hidden;
}

.services-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><defs><radialGradient id="serviceParticle" cx="50%" cy="50%"><stop offset="0%" stop-color="%2306b6d4" stop-opacity="0.05"/><stop offset="100%" stop-color="%2306b6d4" stop-opacity="0"/></radialGradient></defs><circle cx="150" cy="150" r="80" fill="url(%23serviceParticle)"/><circle cx="850" cy="200" r="60" fill="url(%23serviceParticle)"/><circle cx="300" cy="800" r="100" fill="url(%23serviceParticle)"/><circle cx="700" cy="700" r="70" fill="url(%23serviceParticle)"/></svg>') no-repeat;
  background-size: cover;
  animation: serviceParticleFloat 15s ease-in-out infinite;
  opacity: 0.6;
  z-index: 1;
}

@keyframes serviceParticleFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  50% { transform: translateY(-30px) rotate(180deg); }
}

@media (min-width: 768px) {
  .services-section {
    padding: 80px 0;
  }
}

.services-container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 16px;
}

@media (min-width: 640px) {
  .services-container {
    padding: 24px;
  }
}

@media (min-width: 1024px) {
  .services-container {
    padding: 30px;
  }
}

.services-header {
  text-align: center;
  margin-bottom: 48px;
}

@media (min-width: 768px) {
  .services-header {
    margin-bottom: 64px;
  }
}

.services-badge {
  display: inline-block;
  margin-bottom: 24px;
}

.services-badge-text {
  background-color: #06b6d4;
  color: white;
  padding: 8px 24px;
  border-radius: 9999px;
  font-size: 14px;
  font-weight: 500;
}

.services-title {
  font-size: 32px;
  font-weight: bold;
  color: #111827;
  line-height: 1.2;
  margin-bottom: 20px;
}

@media (min-width: 640px) {
  .services-title {
    font-size: 40px;
  }
}

@media (min-width: 768px) {
  .services-title {
    font-size: 48px;
    margin-bottom: 24px;
  }
}

.services-title-highlight {
  padding: 0 20px;
  margin: 0 5px;
  background: linear-gradient(to right, #F0EBFF, #D0DFFF);
  border-radius: 52px;
  line-height: 1;
}

.services-description {
  font-size: 18px;
  color: #475569;
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.services-grid {
  display: flex;
  gap: 20px;
  margin-bottom: 48px;
  overflow-x: auto;
  scroll-snap-type: x mandatory;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none;
  -ms-overflow-style: none;
  padding-bottom: 10px;
}

.services-grid::-webkit-scrollbar {
  display: none;
}

@media (min-width: 640px) {
  .services-grid {
    gap: 24px;
  }
}

@media (min-width: 768px) {
  .services-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 28px;
    margin-bottom: 64px;
    overflow-x: visible;
    scroll-snap-type: none;
    padding-bottom: 0;
  }
}

@media (min-width: 1024px) {
  .services-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 32px;
  }
}

.service-card {
  background-color: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  border: 1px solid #f3f4f6;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 280px;
  flex-shrink: 0;
  scroll-snap-align: start;
  position: relative;
  overflow: hidden;
  animation: fadeInUp 0.8s ease-out forwards;
  opacity: 0;
  transform: translateY(30px);
}

.service-card:nth-child(1) { animation-delay: 0.1s; }
.service-card:nth-child(2) { animation-delay: 0.2s; }
.service-card:nth-child(3) { animation-delay: 0.3s; }
.service-card:nth-child(4) { animation-delay: 0.4s; }
.service-card:nth-child(5) { animation-delay: 0.5s; }
.service-card:nth-child(6) { animation-delay: 0.6s; }

.service-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.1), transparent);
  transition: left 0.5s ease;
}

.service-card:hover::before {
  left: 100%;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (min-width: 768px) {
  .service-card {
    padding: 28px;
    min-width: auto;
    flex-shrink: 1;
    scroll-snap-align: none;
  }
}

@media (min-width: 1024px) {
  .service-card {
    padding: 32px;
  }
}

.service-card:hover {
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  transform: translateY(-8px);
}

.service-icon {
  width: 64px;
  height: 64px;
  background: linear-gradient(to bottom right, #22d3ee, #3b82f6);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  transition: transform 0.3s ease;
}

.service-card:hover .service-icon {
  transform: scale(1.1);
}

.service-icon svg {
  width: 32px;
  height: 32px;
  color: white;
}

.service-title {
  font-size: 24px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 16px;
}

.service-description {
  color: #6b7280;
  margin-bottom: 24px;
  line-height: 1.6;
}

.service-features {
  list-style: none;
  padding: 0;
  margin: 0 0 24px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.service-feature {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #374151;
}

.service-feature-dot {
  width: 8px;
  height: 8px;
  background-color: #06b6d4;
  border-radius: 50%;
  margin-right: 12px;
  flex-shrink: 0;
}

.service-learn-more {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f3f4f6;
}

.service-learn-btn {
  color: #fff;
  font-weight: 500;
  font-size: 14px;
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
  background: #0891b2;
  padding: 8px 12px;
  border-radius: 5px;
}

.service-card:hover .service-learn-btn {
  text-decoration: underline;
}

.service-learn-btn:hover {
  color: #0e7490;
}

.services-cta {
  margin-top: 64px;
  text-align: center;
}

.services-cta-container {
  background: linear-gradient(to right, #ecfeff, #eff6ff);
  border-radius: 24px;
  padding: 48px;
}

.services-cta-title {
  font-size: 30px;
  font-weight: bold;
  color: #111827;
  margin-bottom: 16px;
}

.services-cta-description {
  font-size: 20px;
  color: #6b7280;
  margin-bottom: 32px;
  max-width: 512px;
  margin-left: auto;
  margin-right: auto;
}

.services-cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 16px;
  justify-content: center;
}

@media (min-width: 640px) {
  .services-cta-buttons {
    flex-direction: row;
  }
}

.services-cta-emergency {
  background-color: #ef4444;
  color: white;
  padding: 12px 32px;
  border-radius: 8px;
  font-weight: 500;
  transition: background-color 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.services-cta-emergency:hover {
  background-color: #dc2626;
}

.services-cta-inquiry {
  background-color: #06b6d4;
  color: white;
  padding: 12px 32px;
  border-radius: 8px;
  font-weight: 500;
  transition: background-color 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.services-cta-inquiry:hover {
  background-color: #0891b2;
}

/* Mobile Responsive Styles */
@media (max-width: 768px) {
  .services-section {
    padding: 40px 0;
    width: 100%;
    overflow-x: hidden;
  }

  .services-container {
    padding: 0 16px;
    width: 100%;
    max-width: 100%;
  }

  .services-header {
    margin-bottom: 32px;
    text-align: center;
  }

  .services-title {
    font-size: 28px;
    line-height: 1.2;
  }

  .services-description {
    font-size: 16px;
    padding: 0 12px;
  }

  .services-grid {
    /* display: flex !important; */
    overflow-x: scroll;
    scroll-snap-type: x mandatory;
    gap: 16px;
    /* padding: 0 10px 8px 16px; */
    /* margin: 0 10px; */
    -webkit-overflow-scrolling: touch;
    width: 100%;
  }

  .services-grid::-webkit-scrollbar {
    display: none;
  }

  .service-card {
    min-width: 280px;
    padding: 24px 20px;
    flex: 0 0 auto;
    scroll-snap-align: start;
  }

  .service-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .service-title {
    font-size: 18px;
  }

  .service-description {
    font-size: 15px;
    margin-bottom: 20px;
  }

  .service-feature {
    font-size: 13px;
  }

  .services-cta {
    padding: 72px 30px !important;
    margin-top: 32px;
    border-radius: 15px;
  }

  .services-cta-title {
    font-size: 24px;
  }

  .services-cta-description {
    font-size: 16px;
    margin-bottom: 24px;
  }

  .services-cta-buttons {
    flex-direction: column;
    gap: 12px;
    align-items: center;
  }

  .services-cta-emergency,
  .services-cta-inquiry {
    width: 100%;
    /* max-width: 280px; */
    padding: 14px 24px;
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .services-section {
    padding: 30px 0;
  }

  .services-container {
    padding: 0 12px;
  }

  .services-header {
    margin-bottom: 24px;
  }

  .services-title {
    font-size: 24px;
  }

  .services-description {
    font-size: 15px;
    padding: 0 8px;
  }

  .services-grid {
    gap: 12px;
    width: 100%;
  }

  .service-card {
    min-width: 260px;
    padding: 20px 16px;
  }

  .service-icon {
    width: 44px;
    height: 44px;
    font-size: 18px;
  }

  .service-title {
    font-size: 17px;
  }

  .service-description {
    font-size: 14px;
    margin-bottom: 16px;
  }

  .service-feature {
    font-size: 12px;
  }

  .services-cta {
    padding: 24px 16px;
    margin-top: 24px;
  }

  .services-cta-title {
    font-size: 20px;
  }

  .services-cta-description {
    font-size: 15px;
    margin-bottom: 20px;
  }

  .services-cta-emergency,
  .services-cta-inquiry {
    padding: 12px 20px;
    font-size: 14px;
    width: 100%;
  }
}
