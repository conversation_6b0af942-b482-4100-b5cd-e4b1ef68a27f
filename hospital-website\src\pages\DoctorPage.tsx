import React, { useEffect, useRef } from 'react';
import { Calendar, ArrowRight, Star, Award, Clock, MapPin, Phone, Mail } from 'lucide-react';
import { gsap } from 'gsap';
import { ScrollTrigger } from 'gsap/ScrollTrigger';
import Header from '../components/Header';
import Footer from '../components/Footer';
import './DoctorPage.css';

gsap.registerPlugin(ScrollTrigger);

const DoctorPage: React.FC = () => {
  const pageRef = useRef<HTMLDivElement>(null);
  const headerRef = useRef<HTMLDivElement>(null);
  const cardsRef = useRef<HTMLDivElement>(null);

  // Original website order: https://sdamedicalcentreblr.com/doctors/
  const doctors = [
    {
      id: 1,
      name: 'Dr. <PERSON>',
      specialty: 'Medical Director',
      experience: '20+ Years',
      rating: 4.9,
      reviews: 450,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/<PERSON><PERSON>-670x500.jpg',
      education: 'MD, Medical Administration',
      location: 'Administration Department',
      phone: '+91 **********',
      email: 'raj<PERSON>.<EMAIL>',
      availability: 'Mon - Fri, 9:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Telugu'],
      specializations: ['Medical Administration', 'Healthcare Management', 'Clinical Operations'],
      about: 'As a Medical Director at Seventh Day Adventist Medical Centre hospital I am responsible for overseeing the medical and clinical operations of the hospital.'
    },
    {
      id: 2,
      name: 'Mr. Saji Varghese',
      specialty: 'Administrator',
      experience: '18+ Years',
      rating: 4.7,
      reviews: 320,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Saji-670x500.jpg',
      education: 'MBA Healthcare Management',
      location: 'Administration Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 9:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Malayalam'],
      specializations: ['Healthcare Administration', 'Operations Management', 'Resource Coordination'],
      about: 'I am responsible for overseeing and coordinating the day-to-day operations of a business or organization and ensuring the efficient use of resources to meet the company\'s objectives.'
    },
    {
      id: 3,
      name: 'Mrs. T. Mary Grace',
      specialty: 'Nursing Superintendent',
      experience: '22+ Years',
      rating: 4.8,
      reviews: 380,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/T-mary-670x500.jpg',
      education: 'MSc Nursing',
      location: 'Nursing Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 8:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Tamil'],
      specializations: ['Nursing Administration', 'Patient Care Management', 'Staff Supervision'],
      about: 'A Nursing Superintendent, also known as a Director of Nursing, is a senior-level nursing professional who is responsible for the overall management and administration of the nursing department in a hospital'
    },
    {
      id: 4,
      name: 'Dr. Sunil Abraham Ninan',
      specialty: 'Pediatrician',
      experience: '15+ Years',
      rating: 4.9,
      reviews: 420,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2024/09/dr.-sunil-e1726045288897.png',
      education: 'MD Pediatrics',
      location: 'Pediatrics Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 8:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Malayalam'],
      specializations: ['Child Development', 'Immunizations', 'Neonatal Care'],
      about: 'A pediatrician is a medical doctor who specializes in the care of infants, children, and adolescents.'
    },
    {
      id: 5,
      name: 'Dr. Lavona Ruth Pilli',
      specialty: 'Obstetrician & Gynaecologist',
      experience: '12+ Years',
      rating: 4.8,
      reviews: 340,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2024/03/Untitled-design-2-670x500.jpg',
      education: 'MD OBG',
      location: 'Obstetrics & Gynaecology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 9:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Telugu'],
      specializations: ['Pregnancy Care', 'Gynecological Surgery', 'Women\'s Health'],
      about: 'An Obstetrician and Gynaecologist (OB/GYN) is a medical doctor who specializes in the care of women during pregnancy, childbirth, and the postpartum period, as well as the treatment of reproductive disorders and other gynecological issues.'
    },
    {
      id: 6,
      name: 'Dr. Femi Francis',
      specialty: 'Obstetrician & Gynaecologist',
      experience: '14+ Years',
      rating: 4.7,
      reviews: 320,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2024/03/2-670x500.jpg',
      education: 'MD OBG',
      location: 'Obstetrics & Gynaecology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 9:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Malayalam'],
      specializations: ['Pregnancy Care', 'Gynecological Surgery', 'Women\'s Health'],
      about: 'An Obstetrician and Gynaecologist (OB/GYN) is a medical doctor who specializes in the care of women during pregnancy, childbirth, and the postpartum period, as well as the treatment of reproductive disorders and other gynecological issues.'
    },
    {
      id: 7,
      name: 'Dr. Ashok Rijhwani',
      specialty: 'Paediatric Surgeon',
      experience: '20+ Years',
      rating: 4.8,
      reviews: 280,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Men-Avatar-670x500.jpg',
      education: 'MS Pediatric Surgery',
      location: 'Paediatric Surgery Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Tue - Sat, 8:00 AM - 4:00 PM',
      languages: ['English', 'Hindi', 'Gujarati'],
      specializations: ['Pediatric Surgery', 'Neonatal Surgery', 'Minimally Invasive Surgery'],
      about: 'A pediatric surgeon is a medical doctor who specializes in the surgical treatment of infants, children, and adolescents.'
    },
    {
      id: 8,
      name: 'Mr. Daniel Evans',
      specialty: 'Pharmacy Services',
      experience: '16+ Years',
      rating: 4.6,
      reviews: 250,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Daniel-Evans-670x500.jpg',
      education: 'PharmD',
      location: 'Pharmacy Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 8:00 AM - 8:00 PM',
      languages: ['English', 'Hindi'],
      specializations: ['Clinical Pharmacy', 'Medication Therapy', 'Drug Information'],
      about: 'Pharmacy services in a hospital play a critical role in patient care by providing medication therapy and support to healthcare providers.'
    },
    {
      id: 9,
      name: 'Mr. Shantharaj S',
      specialty: 'Laboratory Services',
      experience: '18+ Years',
      rating: 4.7,
      reviews: 300,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Shantaraj-670x500.jpg',
      education: 'MSc Medical Laboratory Technology',
      location: 'Laboratory Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 7:00 AM - 7:00 PM',
      languages: ['English', 'Hindi', 'Kannada'],
      specializations: ['Clinical Laboratory', 'Diagnostic Testing', 'Quality Control'],
      about: 'Laboratory services in a hospital are a vital component of patient care, providing essential diagnostic information to healthcare providers to support accurate and effective treatment.'
    },
    {
      id: 10,
      name: 'Dr. N S Prakash',
      specialty: 'General Medicine',
      experience: '16+ Years',
      rating: 4.7,
      reviews: 290,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Prakash-670x500.jpg',
      education: 'MD General Medicine',
      location: 'General Medicine Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 8:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Kannada'],
      specializations: ['Internal Medicine', 'Diabetes Care', 'Hypertension Management'],
      about: 'As a General Medicine doctor, we are trained in the prevention, diagnosis, and treatment of a wide range of illnesses and medical conditions.'
    },
    {
      id: 11,
      name: 'Dr. Vandeman Wilson',
      specialty: 'General Medicine',
      experience: '18+ Years',
      rating: 4.7,
      reviews: 310,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Vandeman-670x500.jpg',
      education: 'MD General Medicine',
      location: 'General Medicine Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 8:00 AM - 5:00 PM',
      languages: ['English', 'Hindi'],
      specializations: ['Internal Medicine', 'Diabetes Care', 'Preventive Medicine'],
      about: 'As a General Medicine doctor, you are trained in the prevention, diagnosis, and treatment of a wide range of illnesses and medical conditions.'
    },
    {
      id: 12,
      name: 'Dr. Zain Hussain',
      specialty: 'General Medicine',
      experience: '12+ Years',
      rating: 4.6,
      reviews: 250,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Zain-670x500.jpg',
      education: 'MD General Medicine',
      location: 'General Medicine Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 9:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Urdu'],
      specializations: ['Internal Medicine', 'Emergency Medicine', 'Critical Care'],
      about: 'As a General Medicine doctor, you are trained in the prevention, diagnosis, and treatment of a wide range of illnesses and medical conditions.'
    },
    {
      id: 13,
      name: 'Dr. P. G. Ashok Kumar',
      specialty: 'Cardiologist',
      experience: '18+ Years',
      rating: 4.8,
      reviews: 380,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Avatar-men-670x500.jpg',
      education: 'MD, DM Cardiology',
      location: 'Cardiology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 8:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Kannada'],
      specializations: ['Heart Disease', 'Cardiac Catheterization', 'Preventive Cardiology'],
      about: 'As a cardiac doctor, also known as a cardiologist, my primary area of expertise is the diagnosis and treatment of heart conditions and diseases.'
    },
    {
      id: 14,
      name: 'Dr. Sivasankari R',
      specialty: 'Pulmonologist',
      experience: '14+ Years',
      rating: 4.8,
      reviews: 310,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Untitled-1-670x500.jpg',
      education: 'MD Pulmonology',
      location: 'Pulmonology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Tue - Sat, 9:00 AM - 4:00 PM',
      languages: ['English', 'Hindi', 'Tamil'],
      specializations: ['Respiratory Diseases', 'Asthma Care', 'Sleep Disorders'],
      about: 'A pulmonologist is a medical doctor who specializes in the diagnosis and treatment of diseases and conditions related to the lungs and respiratory system.'
    },
    {
      id: 15,
      name: 'Dr. Aruna Prabhakar',
      specialty: 'Obstetrician & Gynaecologist',
      experience: '16+ Years',
      rating: 4.8,
      reviews: 380,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Aruna-670x500.jpg',
      education: 'MD OBG',
      location: 'Obstetrics & Gynaecology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 9:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Kannada'],
      specializations: ['High Risk Pregnancy', 'Infertility Treatment', 'Laparoscopic Surgery'],
      about: 'An Obstetrician and Gynaecologist (OB/GYN) is a medical doctor who specializes in the care of women during pregnancy, childbirth, and the postpartum period, as well as the treatment of reproductive disorders and other gynecological issues.'
    },
    {
      id: 16,
      name: 'Dr. Kalai Hema K M',
      specialty: 'Obstetrician & Gynaecologist',
      experience: '11+ Years',
      rating: 4.7,
      reviews: 290,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Dr.Kalai-website.jpg',
      education: 'MD OBG',
      location: 'Obstetrics & Gynaecology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Tue - Sat, 10:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Tamil'],
      specializations: ['Pregnancy Care', 'Gynecological Oncology', 'Reproductive Health'],
      about: 'An Obstetrician and Gynaecologist (OB/GYN) is a medical doctor who specializes in the care of women during pregnancy, childbirth, and the postpartum period, as well as the treatment of reproductive disorders and other gynecological issues.'
    },
    {
      id: 17,
      name: 'Dr. Pearlin Raj',
      specialty: 'Obstetrician & Gynaecologist',
      experience: '13+ Years',
      rating: 4.8,
      reviews: 340,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Dr.-Pearlin-Raj-670x500.gif',
      education: 'MD OBG',
      location: 'Obstetrics & Gynaecology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 9:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Tamil'],
      specializations: ['Maternal Care', 'Gynecological Surgery', 'Family Planning'],
      about: 'An Obstetrician and Gynaecologist (OB/GYN) is a medical doctor who specializes in the care of women during pregnancy, childbirth, and the postpartum period, as well as the treatment of reproductive disorders and other gynecological issues.'
    },
    {
      id: 18,
      name: 'Dr. Mahendra Mehta',
      specialty: 'Pediatrician',
      experience: '19+ Years',
      rating: 4.9,
      reviews: 410,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Dr.-Mahendra-670x500.jpg',
      education: 'MD Pediatrics',
      location: 'Pediatrics Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 8:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Gujarati'],
      specializations: ['Child Development', 'Pediatric Cardiology', 'Neonatal Care'],
      about: 'A pediatrician is a medical doctor who specializes in the care of infants, children, and adolescents.'
    },
    {
      id: 19,
      name: 'Dr. Sayeed Ahmed',
      specialty: 'Pediatrician',
      experience: '16+ Years',
      rating: 4.8,
      reviews: 370,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Zyed-670x500.jpg',
      education: 'MD Pediatrics',
      location: 'Pediatrics Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Tue - Sat, 9:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Urdu'],
      specializations: ['Childhood Illnesses', 'Developmental Issues', 'Immunizations'],
      about: 'We have expertise in diagnosing and treating a wide range of childhood illnesses and developmental issues.'
    },
    {
      id: 20,
      name: 'Dr. Farhan Moosa',
      specialty: 'Pediatrician',
      experience: '10+ Years',
      rating: 4.6,
      reviews: 240,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Avatar-men-670x500.jpg',
      education: 'MD Pediatrics',
      location: 'Pediatrics Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 10:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Urdu'],
      specializations: ['Pediatric Care', 'Child Health', 'Growth Monitoring'],
      about: 'A pediatrician specializing in comprehensive healthcare for children from birth through adolescence.'
    },
    {
      id: 21,
      name: 'Dr. Mohammad Gauhar',
      specialty: 'Pediatrician',
      experience: '14+ Years',
      rating: 4.7,
      reviews: 320,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Dr.Isa-Gauhar-670x500.jpg',
      education: 'MD Pediatrics',
      location: 'Pediatrics Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 8:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Urdu'],
      specializations: ['Infant Care', 'Adolescent Medicine', 'Preventive Pediatrics'],
      about: 'We are medical professional who specializes in the healthcare of infants, children, and adolescents.'
    },
    {
      id: 22,
      name: 'Dr. Neehar Patil',
      specialty: 'Paediatric Surgeon',
      experience: '12+ Years',
      rating: 4.6,
      reviews: 260,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/Avatar-Lady-670x500.jpg',
      education: 'MS Pediatric Surgery',
      location: 'Paediatric Surgery Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Wed - Sun, 9:00 AM - 4:00 PM',
      languages: ['English', 'Hindi', 'Marathi'],
      specializations: ['Pediatric Surgery', 'Congenital Anomalies', 'Minimally Invasive Surgery'],
      about: 'A pediatric surgeon specializing in surgical treatment of infants, children, and adolescents.'
    },
    {
      id: 23,
      name: 'Dr. Jasavaroyan',
      specialty: 'Anaesthesiologist',
      experience: '15+ Years',
      rating: 4.7,
      reviews: 300,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Jass-670x500.jpg',
      education: 'MD Anaesthesia',
      location: 'Anaesthesia Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 7:00 AM - 4:00 PM',
      languages: ['English', 'Hindi', 'Tamil'],
      specializations: ['General Anaesthesia', 'Regional Anaesthesia', 'Pain Management'],
      about: 'I am responsible for ensuring the safety and comfort of patients before, during, and after surgical procedures.'
    },
    {
      id: 24,
      name: 'Dr. K.G. Mathew',
      specialty: 'General & Laparoscopic Surgeon',
      experience: '38+ Years',
      rating: 4.9,
      reviews: 520,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/KG-Mathew.jpg',
      education: 'MS General Surgery',
      location: 'General Surgery Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 7:00 AM - 4:00 PM',
      languages: ['English', 'Hindi', 'Malayalam'],
      specializations: ['Laparoscopic Surgery', 'Hernia Surgery', 'Gastroenterology Surgery'],
      about: 'Dr. K G Mathew is a Consultant General and Laparoscopic Surgeon, having 38yrs surgical experience (General surgery, Advanced surgical Gastroenterology, Laparoscopic Surgery, Advanced Hernia surgery)'
    },
    {
      id: 25,
      name: 'Dr. Vijay Wadhwa',
      specialty: 'General & Laparoscopic Surgeon',
      experience: '22+ Years',
      rating: 4.8,
      reviews: 420,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Dr.-Vijay-Wadhwa-670x500.jpg',
      education: 'MS General Surgery',
      location: 'General Surgery Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 8:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Punjabi'],
      specializations: ['Laparoscopic Surgery', 'General Surgery', 'Minimally Invasive Procedures'],
      about: 'General laparoscopic surgery, also known as minimally invasive surgery or keyhole surgery, is a surgical technique that uses small incisions and specialized instruments to perform procedures within the abdominal or pelvic cavities.'
    },
    {
      id: 26,
      name: 'Dr. Akshath',
      specialty: 'General & Laparoscopic Surgeon',
      experience: '8+ Years',
      rating: 4.5,
      reviews: 180,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Men-Avatar-670x500.jpg',
      education: 'MS General Surgery',
      location: 'General Surgery Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Tue - Sat, 9:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Kannada'],
      specializations: ['General Surgery', 'Emergency Surgery', 'Trauma Surgery'],
      about: 'A general surgeon specializing in a wide range of surgical procedures and emergency interventions.'
    },
    {
      id: 27,
      name: 'Dr. Pradeep Kumar',
      specialty: 'Orthopaedic Surgeon',
      experience: '16+ Years',
      rating: 4.7,
      reviews: 330,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/Avatar-670x500.jpg',
      education: 'MS Orthopaedics',
      location: 'Orthopaedics Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Thu, 8:00 AM - 4:00 PM',
      languages: ['English', 'Hindi', 'Telugu'],
      specializations: ['Joint Replacement', 'Spine Surgery', 'Arthroscopy'],
      about: 'An orthopaedic surgeon specializing in the diagnosis and treatment of musculoskeletal disorders.'
    },
    {
      id: 28,
      name: 'Dr. Vedaprakash',
      specialty: 'Orthopaedic Surgeon',
      experience: '17+ Years',
      rating: 4.8,
      reviews: 350,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/Dr.Vedapraksh-670x500.jpg',
      education: 'MS Orthopaedics',
      location: 'Orthopaedics Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Thu, 8:00 AM - 3:00 PM',
      languages: ['English', 'Hindi', 'Kannada'],
      specializations: ['Joint Replacement', 'Trauma Surgery', 'Sports Medicine'],
      about: 'I am responsible for diagnosis, treatment, and management of musculoskeletal conditions and injuries at Seventh day Adventist medical centre'
    },
    {
      id: 29,
      name: 'Dr. Ninan Thomas',
      specialty: 'Radiation Oncologist',
      experience: '18+ Years',
      rating: 4.8,
      reviews: 290,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/Avatar-670x500.jpg',
      education: 'MD Radiation Oncology',
      location: 'Radiation Oncology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 9:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Malayalam'],
      specializations: ['Cancer Treatment', 'Radiation Therapy', 'Oncology Care'],
      about: 'A radiation oncologist specializing in the treatment of cancer using radiation therapy.'
    },
    {
      id: 30,
      name: 'Dr. Claudius Saldanha',
      specialty: 'Radiologist',
      experience: '20+ Years',
      rating: 4.7,
      reviews: 350,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Men-Avatar-670x500.jpg',
      education: 'MD Radiology',
      location: 'Radiology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 8:00 AM - 6:00 PM',
      languages: ['English', 'Hindi', 'Konkani'],
      specializations: ['Medical Imaging', 'CT Scan', 'MRI Interpretation'],
      about: 'A radiologist specializing in medical imaging and diagnostic procedures.'
    },
    {
      id: 31,
      name: 'Dr. David Narayan Rameswarapu',
      specialty: 'Radiologist',
      experience: '15+ Years',
      rating: 4.6,
      reviews: 280,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/David-Narayan-670x500.jpg',
      education: 'MD Radiology',
      location: 'Radiology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Tue - Sat, 9:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Telugu'],
      specializations: ['Diagnostic Imaging', 'Interventional Radiology', 'Medical Image Interpretation'],
      about: 'As a medical professional we specializes in the interpretation of medical images and the performance of diagnostic and interventional procedures using medical imaging techniques.'
    },
    {
      id: 32,
      name: 'Dr. Alice Joseph',
      specialty: 'Ophthalmologist',
      experience: '13+ Years',
      rating: 4.7,
      reviews: 280,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Alice-670x500.jpg',
      education: 'MS Ophthalmology',
      location: 'Ophthalmology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 10:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Malayalam'],
      specializations: ['Eye Surgery', 'Cataract Treatment', 'Retinal Disorders'],
      about: 'Ophthalmology is a medical specialty that deals with the diagnosis and treatment of eye disorders and conditions.'
    },
    {
      id: 33,
      name: 'Dr. Saba Samreen',
      specialty: 'Microbiologist',
      experience: '10+ Years',
      rating: 4.5,
      reviews: 200,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/Avatar-Lady-670x500.jpg',
      education: 'MD Microbiology',
      location: 'Microbiology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 9:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Urdu'],
      specializations: ['Clinical Microbiology', 'Infection Control', 'Laboratory Diagnostics'],
      about: 'A microbiologist specializing in the study of microorganisms and infectious diseases.'
    },
    {
      id: 34,
      name: 'Dr. Julie Joseph',
      specialty: 'Pathologist',
      experience: '14+ Years',
      rating: 4.7,
      reviews: 260,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/Lady-Avatar-670x500.jpg',
      education: 'MD Pathology',
      location: 'Pathology Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sat, 8:00 AM - 4:00 PM',
      languages: ['English', 'Hindi', 'Malayalam'],
      specializations: ['Disease Diagnosis', 'Tissue Analysis', 'Laboratory Medicine'],
      about: 'As a Pathologist we diagnose of diseases by examining tissues, cells, bodily fluids, and organs.'
    },
    {
      id: 35,
      name: 'Dr. Christine. R Deepika',
      specialty: 'Intensive Care Specialist',
      experience: '11+ Years',
      rating: 4.6,
      reviews: 220,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/Avatar-Lady-670x500.jpg',
      education: 'MD Critical Care',
      location: 'Intensive Care Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Sun, 24/7 On-Call',
      languages: ['English', 'Hindi', 'Tamil'],
      specializations: ['Critical Care', 'Emergency Medicine', 'Life Support'],
      about: 'An intensive care specialist providing critical care for patients in life-threatening conditions.'
    },
    {
      id: 36,
      name: 'Dr. Margaret Thomas',
      specialty: 'Anaesthesiologist',
      experience: '16+ Years',
      rating: 4.7,
      reviews: 310,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2023/01/Margaret-670x500.jpg',
      education: 'MD Anaesthesia',
      location: 'Anaesthesia Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Mon - Fri, 7:00 AM - 5:00 PM',
      languages: ['English', 'Hindi', 'Malayalam'],
      specializations: ['Surgical Anaesthesia', 'Pain Management', 'Critical Care Anaesthesia'],
      about: 'We aims to induce a reversible state of unconsciousness, pain relief, muscle relaxation, and physiological stability to ensure a safe and comfortable surgical experience for the patient.'
    },
    {
      id: 37,
      name: 'Dr. Akhila',
      specialty: 'Anaesthesiologist',
      experience: '9+ Years',
      rating: 4.5,
      reviews: 190,
      image: 'https://sdamedicalcentreblr.com/wp-content/uploads/2022/12/Lady-Avatar-670x500.jpg',
      education: 'MD Anaesthesia',
      location: 'Anaesthesia Department',
      phone: '+91 **********',
      email: '<EMAIL>',
      availability: 'Tue - Sat, 8:00 AM - 4:00 PM',
      languages: ['English', 'Hindi', 'Kannada'],
      specializations: ['General Anaesthesia', 'Pediatric Anaesthesia', 'Regional Blocks'],
      about: 'An anaesthesiologist ensuring patient safety and comfort during surgical procedures.'
    }
  ];

  useEffect(() => {
    const ctx = gsap.context(() => {
      // Header animation
      gsap.fromTo(headerRef.current, 
        { opacity: 0, y: 50 },
        { opacity: 1, y: 0, duration: 1, ease: "power3.out" }
      );

      // Cards animation
      gsap.fromTo(".doctor-card", 
        { opacity: 0, y: 100, scale: 0.8 },
        { 
          opacity: 1, 
          y: 0, 
          scale: 1,
          duration: 0.8,
          stagger: 0.2,
          ease: "power3.out",
          scrollTrigger: {
            trigger: cardsRef.current,
            start: "top 80%",
            end: "bottom 20%",
            toggleActions: "play none none reverse"
          }
        }
      );



    }, pageRef);

    return () => ctx.revert();
  }, []);

  return (
    <div className="doctor-page" ref={pageRef}>
      <Header />
      
      {/* Page Header */}
      <section className="page-header" ref={headerRef}>
        <div className="page-header-background">
          <div className="page-header-overlay"></div>
        </div>
        <div className="page-header-container">
          <div className="page-header-content">
            <h1 className="page-header-title">Our Doctors</h1>
            <div className="page-header-breadcrumb">
              <span className="breadcrumb-item">Home</span>
              <span className="breadcrumb-separator">&gt;</span>
              <span className="breadcrumb-item active">Doctors</span>
            </div>
          </div>
        </div>
      </section>

      {/* Doctors Section */}
      <section className="doctors-section">
        <div className="doctors-container">
          {/* Section Header */}
          <div className="doctors-header">
            <div className="doctors-badge">
              <span className="doctors-badge-text">Meet Our Team</span>
            </div>
            <h2 className="doctors-title">
              Expert Medical Professionals
            </h2>
            <p className="doctors-subtitle">
              Our team of highly qualified doctors and specialists are dedicated to providing 
              exceptional healthcare with compassion and expertise.
            </p>
          </div>

          {/* Doctors Grid */}
          <div className="doctors-grid" ref={cardsRef}>
            {doctors.map((doctor) => (
              <div key={doctor.id} className="doctor-card">
                <div className="doctor-card-inner">
                  {/* Front of card */}
                  <div className="doctor-card-front">
                    <div className="doctor-image-container">
                      <img src={doctor.image} alt={doctor.name} className="doctor-image" />
                      <div className="doctor-overlay">
                        <div className="doctor-rating">
                          <Star className="star-icon" />
                          <span>{doctor.rating}</span>
                        </div>
                      </div>
                    </div>
                    <div className="doctor-info">
                      <h3 className="doctor-name">{doctor.name}</h3>
                      <p className="doctor-specialty">{doctor.specialty}</p>
                      <div className="doctor-experience">
                        <Award className="experience-icon" />
                        <span>{doctor.experience} Experience</span>
                      </div>
                      <div className="doctor-reviews">
                        <span>{doctor.reviews} Reviews</span>
                      </div>
                    </div>
                  </div>

                  {/* Back of card */}
                  <div className="doctor-card-back">
                    <div className="doctor-details">
                      <h3 className="doctor-name">{doctor.name}</h3>
                      <p className="doctor-education">{doctor.education}</p>
                      
                      <div className="doctor-contact">
                        <div className="contact-item">
                          <MapPin className="contact-icon" />
                          <span>{doctor.location}</span>
                        </div>
                        <div className="contact-item">
                          <Clock className="contact-icon" />
                          <span>{doctor.availability}</span>
                        </div>
                        <div className="contact-item">
                          <Phone className="contact-icon" />
                          <span>{doctor.phone}</span>
                        </div>
                      </div>

                      <div className="doctor-specializations">
                        <h4>Specializations:</h4>
                        <div className="specialization-tags">
                          {doctor.specializations.map((spec, index) => (
                            <span key={index} className="specialization-tag">{spec}</span>
                          ))}
                        </div>
                      </div>

                      <button className="book-appointment-btn">
                        <Calendar className="btn-icon" />
                        Book Appointment
                        <ArrowRight className="btn-arrow" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default DoctorPage;
